@import 'tailwindcss';

@theme {
  /* 主色调变量 - 基于 Figma 设计 */
  --color-primary-1000: #034040; /* 最深的绿色 */
  --color-primary-900: #0a5c5c; /* 品牌色绿色 */
  --color-primary-700: #1db3a6; /* 中深绿色 */
  --color-primary-500: #7dcecb; /* 中绿色 */
  --color-primary-300: #b2e1de; /* 浅绿色 */
  --color-primary-100: #e1f3f1; /* 非常浅的绿色 */
  --color-primary-50: #f3f9f9; /* 最浅的绿色/白色 */
  /* 兼容旧版本的主色调变量 */
  --color-primary: var(--color-primary-900);

  /* 黑色 */
  --color-black-900: #181818; /* 最深的绿色 */
  --color-black-700: #222222; /* 中深绿色 */
  --color-black-500: #282828; /* 中绿色 */
  /* 兼容旧版本的主色调变量 */
  --color-black: var(--color-black-900);

  /* 阴影大小 */
  --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 4px 0 rgb(0 0 0 / 0.08), 0 0 2px -1px rgb(0 0 0 / 0.07);
  --shadow-md: 0 4px 5px -1px rgb(0 0 0 / 0.03), 0 2px 5px -1px rgb(0 0 0 / 0.08);
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 按钮动效类 */
  .btn-effect {
    @apply inline-flex items-center justify-center rounded-md cursor-pointer transition-all duration-200 relative;
    /* 添加相对定位，为伪元素做准备 */
  }

  /* 悬停效果 - 背景变化和放大（使用伪元素） */
  .btn-effect::before {
    content: '';
    @apply absolute inset-0 rounded-md bg-transparent transition-all duration-200 z-0;
    z-index: -1; /* 确保伪元素在内容之下 */
    transform: scale(0.8);
    opacity: 0;
  }

  .btn-effect:hover::before {
    @apply bg-gray-100;
    transform: scale(1.1);
    opacity: 1;
  }

  /* 确保内容在伪元素之上 */
  .btn-effect > * {
    @apply relative;
  }

  /* 点击效果 - 整体缩小并恢复 */
  .btn-effect:active {
    @apply scale-95;
    transform-origin: center;
    transition-duration: 100ms;
  }

  .btn-effect:active::before {
    @apply bg-gray-100;
  }

  /* 图标按钮尺寸变体 */
  .btn-effect-sm {
    @apply p-1.5 text-sm;
  }

  .btn-effect-md {
    @apply p-2 text-base;
  }

  .btn-effect-lg {
    @apply p-3 text-lg;
  }
}

/* 全局默认样式 */
:root {
  min-height: 100vh;
  color: var(--color-black);
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
