import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getTaxonomyHierarchy } from '@/api/taxonomy'

// 定义分类数据的接口
interface TaxonomySpecies {
  id: number
  name: string
  count?: number
}

interface TaxonomyGenus {
  name: string
  species: TaxonomySpecies[]
}

interface TaxonomyFamily {
  family: string
  genera: TaxonomyGenus[]
}

export const useTaxonomyStore = defineStore('taxonomy', () => {
  // 状态 (state)
  const taxonomyData = ref<TaxonomyFamily[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // 计算属性 (getters)
  const families = computed(() => {
    return taxonomyData.value.map((family) => family.family)
  })

  const genusCount = computed(() => {
    return taxonomyData.value.reduce((total, family) => {
      return total + family.genera.length
    }, 0)
  })

  const speciesCount = computed(() => {
    return taxonomyData.value.reduce((total, family) => {
      const familySpeciesCount = family.genera.reduce((genusTotal, genus) => {
        return genusTotal + genus.species.length
      }, 0)
      return total + familySpeciesCount
    }, 0)
  })

  // 方法 (actions)
  const fetchTaxonomyData = async () => {
    // 如果已经有数据且不是强制刷新，则直接返回
    if (taxonomyData.value.length > 0 && !loading.value) {
      return
    }

    loading.value = true
    error.value = null

    try {
      const response = await getTaxonomyHierarchy()

      if (response.code === 200) {
        taxonomyData.value = response.data
        lastUpdated.value = new Date()
      } else {
        error.value = response.msg || '获取分类数据失败'
        console.error('获取分类数据失败:', response.msg)

        // 如果API请求失败，使用默认数据（仅用于开发和演示）
        useFallbackData()
      }
    } catch (err: any) {
      error.value = err.message || '获取分类数据时发生错误'
      console.error('获取分类数据错误:', err)

      // 如果API请求出错，使用默认数据（仅用于开发和演示）
      useFallbackData()
    } finally {
      loading.value = false
    }
  }

  // 使用默认数据（仅用于开发和演示）
  const useFallbackData = () => {
    taxonomyData.value = [
      {
        family: '秋海棠科',
        genera: [
          {
            name: '秋海棠属',
            species: [
              { id: 101, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 102, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 103, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 104, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 105, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
          {
            name: '球根秋海棠属',
            species: [
              { id: 106, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 107, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
        ],
      },
      {
        family: '天南星科',
        genera: [
          {
            name: '龟背竹属',
            species: [
              { id: 201, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 202, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 203, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 204, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 205, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 206, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
          {
            name: '花烛属',
            species: [
              { id: 207, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 208, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
        ],
      },
      {
        family: '景天科',
        genera: [
          {
            name: '景天属',
            species: [
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
          {
            name: '拟石莲花属',
            species: [
              { id: 304, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 305, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 306, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
        ],
      },
      {
        family: '景天科',
        genera: [
          {
            name: '景天属',
            species: [
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
          {
            name: '拟石莲花属',
            species: [
              { id: 304, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 305, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 306, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
        ],
      },
      {
        family: '景天科',
        genera: [
          {
            name: '景天属',
            species: [
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
          {
            name: '拟石莲花属',
            species: [
              { id: 304, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 305, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 306, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
        ],
      },
      {
        family: '景天科',
        genera: [
          {
            name: '景天属',
            species: [
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
          {
            name: '拟石莲花属',
            species: [
              { id: 304, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 305, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 306, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
        ],
      },
      {
        family: '景天科',
        genera: [
          {
            name: '景天属',
            species: [
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
          {
            name: '拟石莲花属',
            species: [
              { id: 304, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 305, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 306, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
        ],
      },
      {
        family: '景天科',
        genera: [
          {
            name: '景天属',
            species: [
              { id: 301, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 302, name: '重裂鹿角蕨', latinName: 'Platycerium bifurcatum' },
              { id: 303, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
          {
            name: '拟石莲花属',
            species: [
              { id: 304, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 305, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
              { id: 306, name: '秋海棠', latinName: 'Platycerium bifurcatum' },
            ],
          },
        ],
      },
    ]
    lastUpdated.value = new Date()
  }

  // 重置数据
  const resetTaxonomyData = () => {
    taxonomyData.value = []
    error.value = null
    lastUpdated.value = null
  }

  // 返回所有状态和方法
  return {
    // 状态
    taxonomyData,
    loading,
    error,
    lastUpdated,

    // 计算属性
    families,
    genusCount,
    speciesCount,

    // 方法
    fetchTaxonomyData,
    resetTaxonomyData,
  }
})
