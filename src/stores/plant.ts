import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getPlantSpeciesDetail, getPlantSpeciesDetailByLatinName } from '@/api/plant'
import type { PlantSpecies } from '@/types/plant'

export const usePlantStore = defineStore('plant', () => {
  // 状态 (state)
  const plantDetail = ref<PlantSpecies | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)

  // 计算属性 (getters) - 基本信息
  const basicInfo = computed(() => {
    if (!plantDetail.value) return null

    return {
      id: plantDetail.value.id,
      name: {
        chinese: plantDetail.value.name?.chinese || '',
        latin: plantDetail.value.name?.latin || '',
        english: plantDetail.value.name?.english || '',
      },
      alias: plantDetail.value.alias?.split(',') || [],
      description: plantDetail.value.description || '',
      rarity: plantDetail.value.rarity || 0,
      difficulty: plantDetail.value.difficulty || 0,
      image: plantDetail.value.image || plantDetail.value.images?.[0]?.image_url || '',
    }
  })

  console.log('basicInfo', basicInfo.value)

  // 计算属性 - 分类信息
  const taxonomy = computed(() => {
    if (!plantDetail.value) return null

    return {
      family: {
        id: plantDetail.value.family?.id,
        name: {
          chinese: plantDetail.value.family?.name?.chinese || '',
          latin: plantDetail.value.family?.name?.latin || '',
          english: plantDetail.value.family?.name?.english || '',
        },
        description: plantDetail.value.family?.description || '',
      },
      genus: {
        id: plantDetail.value.genus?.id,
        name: {
          chinese: plantDetail.value.genus?.name?.chinese || '',
          latin: plantDetail.value.genus?.name?.latin || '',
          english: plantDetail.value.genus?.name?.english || '',
        },
        description: plantDetail.value.genus?.description || '',
      },
    }
  })

  // 计算属性 - 生长条件
  const growthConditions = computed(() => {
    if (!plantDetail.value) return null

    return {
      temperature: {
        range: plantDetail.value.temperature_range || { min: null, max: null },
        extreme: plantDetail.value.temperature_extreme || { min: null, max: null },
      },
      light: {
        range: plantDetail.value.light_range || { min: null, max: null },
        extreme: plantDetail.value.light_extreme || { min: null, max: null },
      },
      humidity: {
        range: plantDetail.value.humidity_range || { min: null, max: null },
        extreme: plantDetail.value.humidity_extreme || { min: null, max: null },
      },
      watering: {
        style: plantDetail.value.watering_style || '',
        notes: plantDetail.value.watering_notes || '',
      },
      soil: plantDetail.value.soil_mix || '',
      soil_ph: plantDetail.value.soil_ph || { min: null, max: null },
      soil_notes: plantDetail.value.soil_notes || '',
      native_climate_zone: plantDetail.value.native_climate_zone || '',
      origin: plantDetail.value.origin || [],
    }
  })

  console.log('growthConditions', growthConditions.value)

  // 计算属性 - 繁殖方式
  const propagation = computed(() => {
    if (!plantDetail.value) return null

    return {
      sexual: plantDetail.value.sexual_propagation || false,
      asexual: plantDetail.value.asexual_propagation || [],
    }
  })

  // 计算属性 - 图片集
  const images = computed(() => {
    if (!plantDetail.value?.images) return []

    return plantDetail.value.images
      .sort((a, b) => a.display_order - b.display_order)
      .map((img) => ({
        id: img.id,
        url: img.image_url,
        caption: img.caption || '',
        isPrimary: img.is_primary || false,
      }))
  })

  // 方法 (actions)
  async function fetchPlantDetail(speciesId: number) {
    if (loading.value) return // 防止重复请求

    loading.value = true
    error.value = null

    try {
      const response = await getPlantSpeciesDetail(speciesId)

      if (response.code === 200) {
        plantDetail.value = response.data
        lastUpdated.value = new Date()
      } else {
        error.value = response.msg || '获取植物详情失败'
        console.error('获取植物详情失败:', response.msg)
      }
    } catch (err: any) {
      error.value = err.message || '获取植物详情时发生错误'
      console.error('获取植物详情错误:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据 URL 中的拉丁名查询植物详情
   * @param urlLatinName URL 中的拉丁名（用下划线分隔的多个单词）
   */
  async function fetchPlantDetailByLatinName(urlLatinName: string) {
    if (loading.value) return // 防止重复请求

    loading.value = true
    error.value = null

    try {
      // 将 URL 中的下划线替换为空格，获得正确的拉丁名格式
      const formattedLatinName = urlLatinName.replace(/_/g, ' ')

      const response = await getPlantSpeciesDetailByLatinName(formattedLatinName)

      if (response.code === 200) {
        plantDetail.value = response.data
        lastUpdated.value = new Date()
      } else {
        error.value = response.msg || '获取植物详情失败'
        console.error('获取植物详情失败:', response.msg)
      }
    } catch (err: any) {
      error.value = err.message || '获取植物详情时发生错误'
      console.error('获取植物详情错误:', err)
    } finally {
      loading.value = false
    }
  }

  function resetPlantDetail() {
    plantDetail.value = null
    error.value = null
    lastUpdated.value = null
  }

  // 返回所有状态和方法
  return {
    // 原始状态
    plantDetail,
    loading,
    error,
    lastUpdated,

    // 结构化计算属性
    basicInfo,
    taxonomy,
    growthConditions,
    propagation,
    images,

    // 方法
    fetchPlantDetail,
    fetchPlantDetailByLatinName,
    resetPlantDetail,
  }
})
