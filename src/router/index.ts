import { createRouter, createWebHistory } from 'vue-router'
import DefaultLayout from '@/components/layout/DefaultLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'DefaultLayout',
      component: DefaultLayout,
      children: [
        // {
        //   path: '',
        //   name: 'home',
        //   component: () => import('../views/HomeView.vue'),
        // },
        // {
        //   path: 'plant/:id',
        //   name: 'plantById',
        //   component: () => import('../views/PlantDetailView.vue'),
        // },
        {
          path: 'plant/:latin_name',
          name: 'plantByLatinName',
          component: () => import('../views/PlantDetailView.vue'),
        },
        {
          path: 'plant/taxonomy',
          name: 'taxonomy',
          component: () => import('../views/TaxonomyView.vue'),
        },
        {
          path: 'plant/category',
          name: 'category',
          component: () => import('../views/CategoryView.vue'),
        },
        {
          path: 'learning',
          name: 'learning',
          component: () => import('../views/LearningView.vue'),
        },
        {
          // 捕获所有 /learning/ 下的子路径
          path: 'learning/:pathMatch(.*)*',
          name: 'learningContent',
          component: () => import('../views/LearningView.vue'),
        },
      ],
    },

    {
      path: '',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
    },

    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
  ],
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置且用户使用了浏览器的前进/后退按钮
    if (savedPosition) {
      return savedPosition
    }
    // 如果有哈希值，滚动到对应元素
    if (to.hash) {
      return { el: to.hash }
    }
    // 否则滚动到顶部
    return { top: 0 }
  },
})

export default router
