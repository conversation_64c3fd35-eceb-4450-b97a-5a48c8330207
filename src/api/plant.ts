import service from '@/utils/request'
import type { ApiResponse } from '@/types/api'
import type { PlantSpecies } from '@/types/plant'

/**
 * 获取植物种类详情
 * @param speciesId 植物种类ID
 * @returns 植物种类详情数据
 */
export const getPlantSpeciesDetail = (speciesId: number) => {
  console.log('【api/plant.ts】调用 API 获取植物详情，ID:', speciesId)

  return service.request<ApiResponse<PlantSpecies>>({
    method: 'get',
    url: '/plant/species/by_id',
    params: {
      species_id: speciesId,
    },
  })
}

/**
 * 基于植物的latin name了解植物详情
 * @param speciesLatinName 植物拉丁名
 * @returns 植物种类详情数据
 */
export const getPlantSpeciesDetailByLatinName = (speciesLatinName: string) => {
  console.log('【api/plant.ts】调用 API 获取植物详情，拉丁名:', speciesLatinName)

  return service.request<ApiResponse<PlantSpecies>>({
    method: 'get',
    url: '/plant/species/by_latin_name',
    params: {
      species_latin_name: speciesLatinName,
    },
  })
}
