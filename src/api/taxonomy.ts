import service from '@/utils/request'
import type { ApiResponse } from '@/types/api'
import type { PlantFamily, PlantGenus, PlantSpecies } from '@/types/plant'

/**
 * 获取所有植物科列表
 * @returns 植物科列表数据
 */
export const getAllFamilies = () => {
  return service.request<ApiResponse<PlantFamily[]>>({
    method: 'get',
    url: '/plant/families',
  })
}

/**
 * 获取特定科下的所有属
 * @param familyId 科ID
 * @returns 植物属列表数据
 */
export const getGeneraByFamily = (familyId: number) => {
  return service.request<ApiResponse<PlantGenus[]>>({
    method: 'get',
    url: '/plant/genera',
    params: {
      family_id: familyId,
    },
  })
}

/**
 * 获取特定属下的所有种
 * @param genusId 属ID
 * @returns 植物种列表数据
 */
export const getSpeciesByGenus = (genusId: number) => {
  return service.request<ApiResponse<PlantSpecies[]>>({
    method: 'get',
    url: '/plant/species',
    params: {
      genus_id: genusId,
    },
  })
}

/**
 * 获取完整的分类层次结构（科-属-种）
 * @returns 完整的分类层次结构数据
 */
export const getTaxonomyHierarchy = () => {
  return service.request<ApiResponse<any>>({
    method: 'get',
    url: '/plant/taxonomy/hierarchy',
  })
}
