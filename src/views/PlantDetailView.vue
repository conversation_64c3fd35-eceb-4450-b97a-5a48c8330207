<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { Icon } from '@iconify/vue'

import Faq from '@/components/common/faq/Faq.vue'
import KnowledgeSidebar from '@/components/knowledge/KnowledgeSidebar.vue'
import RandomPattern from '@/components/pattern/RandomPattern.vue'
import LongText from '@/components/longtext/LongText.vue'

import { usePlantStore } from '@/stores/plant'

// 获取路由参数
const route = useRoute()
const latinName = ref((route.params.latin_name as string) || '') // 获取 URL 中的拉丁名参数

// 使用植物 Store
const plantStore = usePlantStore()
// 使用 storeToRefs 解构 store 的 state，保持响应式
const { loading, error, basicInfo, taxonomy, images, growthConditions } = storeToRefs(plantStore)

// 当前显示的图片索引
const currentImageIndex = ref(0)

// 用于强制重新渲染 RandomPattern 组件的 key
const patternKey = ref(0)

// 切换图片
const changeImage = (index: number) => {
  currentImageIndex.value = index
  // 更新 patternKey，强制重新渲染 RandomPattern 组件
  patternKey.value++
}

// 获取图片列表
const plantImages = computed(() => {
  // 如果有API返回的图片，优先使用
  // if (images.value && images.value.length > 0) {
  //   return images.value.map((img) => img.url)
  // }

  // 否则使用默认图片
  return [
    '/src/assets/plant_image_1.png',
    '/src/assets/plant_image_2.png',
    '/src/assets/plant_image_3.png',
    '/src/assets/plant_image_4.png',
    '/src/assets/plant_image_5.png',
    '/src/assets/plant_image_6.png',
  ]
})

// 在组件挂载后获取植物详情数据
onMounted(async () => {
  console.log('onMounted()组件挂载完毕，开始渲染>>>>>>>>>')
  try {
    if (latinName.value) {
      await plantStore.fetchPlantDetailByLatinName(latinName.value)
      console.log('growthConditions~~', growthConditions.value)
      console.log('onMounted()通过拉丁名获取植物详情成功')
    }
  } catch (err) {
    console.error('onMounted()获取植物详情失败:', err)
  }
})

// 监听路由参数变化，重新获取植物详情
watch(
  () => route.params.latin_name,
  async (newLatinName) => {
    if (newLatinName) {
      console.log('路由参数变化，新拉丁名:', newLatinName)
      latinName.value = newLatinName as string
      await plantStore.fetchPlantDetailByLatinName(latinName.value)
    }
  },
)
</script>

<template>
  <!-- 加载中状态 -->
  <div v-if="loading" class="flex justify-center items-center h-screen">
    <div class="text-center">
      <p class="text-xl text-primary-600 mb-4">正在加载植物信息...</p>
      <!-- 这里可以添加一个加载动画 -->
    </div>
  </div>

  <!-- 错误状态 -->
  <div v-else-if="error" class="flex justify-center items-center h-screen">
    <div class="text-center">
      <p class="text-xl text-primary-900 mb-4">加载失败: {{ error }}</p>
      <button
        @click="plantStore.fetchPlantDetailByLatinName(latinName)"
        class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
      >
        重试
      </button>
    </div>
  </div>

  <!-- 植物详情主区域 - 使用 CSS Grid 布局 -->
  <div
    v-else-if="basicInfo"
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-24 gap-2 max-w-7xl mx-auto"
  >
    <!-- 左侧：植物名称和描述 - 移动端全宽，平板占一半，桌面端占3列 -->
    <div class="py-6 lg:col-span-6 order-2 md:order-1 lg:h-[600px] flex flex-col justify-between">
      <!-- 植物名称和拉丁名 -->
      <div class="relative">
        <h1 class="text-3xl md:text-4xl font-extrabold mb-2 relative z-10">
          {{ basicInfo.name.chinese }}
        </h1>
        <p
          class="absolute left-6 -top-6 text-gray-200/70 font-extrabold text-2xl md:text-3xl italic whitespace-nowrap overflow-visible"
        >
          {{ basicInfo.name.latin }}
        </p>
        <div class="text-sm text-gray-600 flex items-center">
          <span v-if="taxonomy?.family?.name?.chinese">{{ taxonomy.family.name.chinese }}</span>
          <Icon
            v-if="taxonomy?.family?.name?.chinese && taxonomy?.genus?.name?.chinese"
            icon="ic:round-arrow-right"
            width="14"
            height="14"
            class="mx-1"
          />
          <span v-if="taxonomy?.genus?.name?.chinese">{{ taxonomy.genus.name.chinese }}</span>
        </div>
      </div>

      <!-- 植物描述 -->
      <LongText
        :clamp="10"
        expandMode="fullscreen"
        class="leading-7 md:leading-8 text-gray-500 tracking-widest"
      >
        {{ basicInfo.description }}
      </LongText>

      <!-- 缩略图选择器 -->
      <div class="flex space-x-4 justify-center md:justify-end px-4">
        <div
          v-for="(image, index) in plantImages"
          :key="index"
          @click="changeImage(index)"
          class="w-8 h-8 rounded-full overflow-hidden cursor-pointer border-1"
          :class="
            currentImageIndex === index
              ? 'border-primary-900 border-[2px] opacity-100'
              : 'border-gray-100 opacity-50'
          "
        >
          <img :src="image" alt="缩略图" class="w-full h-full object-cover" />
        </div>
      </div>
    </div>

    <!-- 中间：植物图片 - 移动端全宽并置顶，平板占一半，桌面端占6列 -->
    <div
      class="lg:col-span-12 order-1 md:order-2 flex justify-center lg:justify-end items-start lg:pt-10"
    >
      <!-- 主图片容器 - 设置最小和最大宽度限制，防止图片过分缩小或放大 -->
      <div class="relative w-full max-w-2xl mx-auto overflow-hidden">
        <!-- 图片 - 使用 object-contain 确保图片比例不变形，同时设置最小高度 -->
        <img
          :src="plantImages[currentImageIndex]"
          :alt="basicInfo.name.chinese"
          class="w-full h-auto min-h-[200px] md:min-h-[300px] lg:h-[600px] object-contain mx-auto"
        />
        <div class="absolute top-0 w-full h-full flex justify-center">
          <RandomPattern :size="400" color="#0A5C5C" :opacity="0.1" :key="patternKey" />
        </div>
      </div>
    </div>

    <!-- 右侧：知识侧边栏 - 移动端全宽，平板端全宽(位于下方)，桌面端占3列 -->
    <div class="md:col-span-6 order-3">
      <KnowledgeSidebar
        :light-range="growthConditions.light.range"
        :light-extreme="growthConditions.light.extreme"
        :temperature-range="growthConditions.temperature.range"
        :temperature-extreme="growthConditions.temperature.extreme"
        :humidity-range="growthConditions.humidity.range"
        :humidity-extreme="growthConditions.humidity.extreme"
        :soil-mix="growthConditions.soil"
        :soil_ph="growthConditions.soil_ph"
        :soil_notes="growthConditions.soil_notes"
      />
    </div>
  </div>

  <!-- FAQ 部分 - 保持不变 -->
  <div class="mt-16 max-w-7xl mx-auto">
    <Faq />
  </div>
</template>

<style scoped>
@import '@/assets/pattern.min.css';
</style>
