<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Pagination from '@/components/common/pagination/Pagination.vue'

// 定义分类数据类型
interface Category {
  id: number
  name: string
  description: string
  image: string
  count: number
}

// 定义植物数据类型
interface Plant {
  id: number
  name: string
  latinName: string
  image: string
  description: string
}

// 存储各种测量值
const headerHeight = ref(0)
const windowHeight = ref(0)
const mainContentRef = ref<HTMLElement | null>(null)

// 计算侧边栏的样式
const sidebarStyle = computed(() => {
  return {
    position: 'sticky',
    top: `${headerHeight.value}px`,
    height: `calc(100vh - ${headerHeight.value}px)`,
    overflowY: 'auto' as const,
  }
})

// 计算主内容区域的最小高度
const mainMinHeight = computed(() => {
  return `calc(100vh - ${headerHeight.value}px)`
})

// 更新各种测量值
const updateMeasurements = () => {
  // 获取 header 高度
  const header = document.querySelector('header')
  if (header) {
    headerHeight.value = header.getBoundingClientRect().height
  }

  // 获取窗口高度
  windowHeight.value = window.innerHeight
}

// 窗口大小变化事件处理函数
const handleResize = () => {
  updateMeasurements()
}

// Mock 分类数据
const categories = ref<Category[]>([
  {
    id: 1,
    name: '秋海棠',
    description:
      '鳟鱼秋海棠是秋海棠科属多年生亚灌木植物，其实它是斑叶竹节秋海棠的一个栽培品种。如果是地栽的鳟鱼海棠，它可以长到两米多高。它的树形又直立又美观，和竹节海棠是近亲鳟鱼秋海棠是秋海棠科属多年生亚灌木植物，其实它是斑叶竹节秋海棠的一个栽培品种。如果是地栽的鳟鱼海棠，它可以长到两米多高。它的树形又直立又美观，和竹节海棠是近亲...',
    image: '/src/assets/category/kuaigen.png',
    count: 42,
  },
  {
    id: 2,
    name: '多肉植物',
    description: '各种可爱的多肉和仙人掌',
    image: '/src/assets/category/kuaigen.png',
    count: 36,
  },
  {
    id: 3,
    name: '蔓绿绒',
    description: '以观赏叶片为主的植物',
    image: '/src/assets/category/kuaigen.png',
    count: 28,
  },
  {
    id: 4,
    name: '花烛',
    description: '各种美丽的开花植物',
    image: '/src/assets/category/kuaigen.png',
    count: 33,
  },
  {
    id: 5,
    name: '龟背竹',
    description: '神奇的食虫植物品种',
    image: '/src/assets/category/kuaigen.png',
    count: 15,
  },
  {
    id: 6,
    name: '蔓绿绒',
    description: '以观赏叶片为主的植物',
    image: '/src/assets/category/kuaigen.png',
    count: 28,
  },
  {
    id: 7,
    name: '花烛',
    description: '各种美丽的开花植物',
    image: '/src/assets/category/kuaigen.png',
    count: 33,
  },
  {
    id: 8,
    name: '龟背竹',
    description: '神奇的食虫植物品种',
    image: '/src/assets/category/kuaigen.png',
    count: 15,
  },
  {
    id: 9,
    name: '蔓绿绒',
    description: '以观赏叶片为主的植物',
    image: '/src/assets/category/kuaigen.png',
    count: 28,
  },
  {
    id: 10,
    name: '花烛',
    description: '各种美丽的开花植物',
    image: '/src/assets/category/kuaigen.png',
    count: 33,
  },
])

// 当前选中的分类ID
const selectedCategoryId = ref(1)

// 分页相关
const currentPage = ref(1)
const totalPages = ref(10)

const router = useRouter()

// 处理分类点击
const handleCategoryClick = (categoryId: number) => {
  selectedCategoryId.value = categoryId

  // 重置滚动位置 - 使用多种方法确保在不同环境下都能正常工作
  // 1. 使用 ref 引用的元素
  if (mainContentRef.value) {
    mainContentRef.value.scrollTop = 0
  }

  // 2. 滚动整个页面到顶部（适用于移动设备）
  window.scrollTo({ top: 0, behavior: 'smooth' })

  // 3. 滚动主内容区域的父元素
  const mainContentParent = document.querySelector('.flex.flex-col.md\\:flex-row.gap-10')
  if (mainContentParent) {
    mainContentParent.scrollTop = 0
  }

  // 4. 滚动文档主体
  document.body.scrollTop = 0
  document.documentElement.scrollTop = 0

  // 重置分页到第一页
  currentPage.value = 1

  // 这里可以添加获取分类详情的逻辑
}

// 处理分页点击
const handlePageClick = (page: number) => {
  currentPage.value = page
  // 这里可以添加分页逻辑
}

// 处理植物点击
const handlePlantClick = (plantId: number) => {
  // router.push(`/plant/${plantId}`)
  router.push({ name: 'plantByLatinName', params: { latin_name: 'Platycerium_bifurcatum' } })
}

// 模拟植物数据 - 列表形式
const plants = ref<Plant[]>(
  Array.from({ length: 10 }, (_, i) => ({
    id: i + 1,
    name: `植物分类名称`,
    latinName: `Plantus example ${i + 1}`,
    image: '/src/assets/category/kuaigen.png',
    description:
      '植物分类描述文本，这里是对该分类下植物的一个简短介绍，可以包含该分类的特点和适合种植的环境等信息。鳟鱼秋海棠是秋海棠科属多年生亚灌木植物，其实它是斑叶竹节秋海棠的一个栽培品种。如果是地栽的鳟鱼海棠，它可以长到两米多高。它的树形又直立又美观，和竹节海棠是近亲...',
  })),
)

// 组件挂载时
onMounted(() => {
  // 初始化测量值
  updateMeasurements()

  // 添加事件监听器
  window.addEventListener('resize', handleResize)

  // 确保在 DOM 完全渲染后再次更新测量值
  setTimeout(updateMeasurements, 100)
})

// 组件卸载时
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div class="">
    <!-- 在移动端显示的横向分类导航 -->
    <div
      class="md:hidden fixed top-[72px] left-0 right-0 z-40 px-2 bg-white border-b-[0.5px] border-gray-200 shadow-xs"
    >
      <div class="overflow-x-auto scrollbar-hide">
        <ul class="flex space-x-2 py-2 whitespace-nowrap">
          <li
            v-for="category in categories"
            :key="category.id"
            @click="handleCategoryClick(category.id)"
            class="cursor-pointer transition-colors duration-200 rounded-md px-5 py-2"
            :class="
              selectedCategoryId === category.id
                ? 'bg-gray-50 text-primary-900 font-medium'
                : 'hover:bg-gray-50'
            "
          >
            <div class="flex items-center">
              <h3 class="tracking-wider">{{ category.name }}</h3>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!-- 主容器，替代SidebarLayout -->
    <div class="max-w-7xl mx-auto w-full">
      <div class="flex flex-col md:flex-row gap-10">
        <!-- 侧边栏区域（仅在桌面端显示） -->
        <div class="w-full hidden md:block md:w-1/4" :style="sidebarStyle">
          <div class="w-full mb-8">
            <div class="bg-white rounded-lg p-4">
              <ul class="space-y-2">
                <li
                  v-for="category in categories"
                  :key="category.id"
                  @click="handleCategoryClick(category.id)"
                  class="px-5 py-1 cursor-pointer transition-colors duration-200 rounded-md overflow-hidden"
                  :class="selectedCategoryId === category.id ? 'bg-gray-100' : 'hover:bg-gray-50'"
                >
                  <div class="flex items-center p-2">
                    <div>
                      <h3 class="font-medium text-gray-900 tracking-wider">{{ category.name }}</h3>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div ref="mainContentRef" class="w-full md:w-3/4" :style="{ minHeight: mainMinHeight }">
          <!-- 移动端顶部内边距 -->
          <div class="block md:hidden h-16"></div>

          <!-- 当前分类信息 -->
          <div class="mb-10 pt-6 relative rounded-lg bg-gray-50">
            <!-- 分类内容 -->
            <div class="relative p-6">
              <div class="flex flex-col items-start">
                <!-- 分类信息 -->
                <div class="w-full">
                  <div class="flex items-center gap-3 mb-2">
                    <h2 class="text-3xl font-bold text-gray-800">
                      {{ categories.find((c) => c.id === selectedCategoryId)?.name || '所有植物' }}
                    </h2>
                    <span
                      class="px-2 py-1 font-bold bg-primary-100 rounded-lg text-sm text-primary-700"
                    >
                      在册{{ categories.find((c) => c.id === selectedCategoryId)?.count || 0 }}种
                    </span>
                  </div>
                  <p class="text-gray-600 mb-4 line-clamp-2">
                    {{
                      categories.find((c) => c.id === selectedCategoryId)?.description ||
                      '浏览所有植物分类'
                    }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 植物列表 list -->
          <div class="mb-8">
            <div
              v-for="plant in plants"
              :key="plant.id"
              class="group flex flex-col md:flex-row mb-10 bg-white hover:bg-gray-50 overflow-hidden rounded-lg cursor-pointer transition-colors duration-300"
              @click="handlePlantClick(plant.id)"
            >
              <!-- 植物图片 -->
              <div class="relative w-full md:w-1/5 aspect-square overflow-hidden">
                <img
                  :src="plant.image"
                  :alt="plant.name"
                  class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
                />
              </div>

              <!-- 植物信息 -->
              <div class="w-full md:w-4/5 p-3 md:p-8 flex flex-col justify-between">
                <div class="flex items-center gap-2 mb-2">
                  <h3
                    class="tracking-wider text-xl font-bold text-gray-800 group-hover:text-primary-900 transition-colors duration-300"
                  >
                    {{ plant.name }}
                  </h3>
                  <p class="text-lg text-gray-200 font-bold italic tracking-wider">
                    {{ plant.latinName }}
                  </p>
                </div>
                <p class="text-md text-gray-600 line-clamp-2 tracking-wider">
                  {{ plant.description }}
                </p>
              </div>
            </div>
          </div>

          <!-- 分页控件 -->
          <Pagination
            :total-pages="totalPages"
            :current-page="currentPage"
            @update:current-page="handlePageClick"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* @import '@/assets/pattern.min.css'; */
</style>
