<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import TaxonomyList from '@/components/taxonomy/TaxonomyList.vue'
import { useTaxonomyStore } from '@/stores/taxonomy'
import taxonomyBg from '@/assets/taxonomyBg.jpg'

const router = useRouter()
const taxonomyStore = useTaxonomyStore()

// 处理种点击事件
const handleSpeciesClick = (species) => {
  // 将中文名转换为拉丁名的URL格式（这里假设我们有拉丁名，实际应用中可能需要调整）
  const latinNameUrl = species.latinName
    ? species.latinName.replace(/\s/g, '_')
    : `species-${species.id}`
  router.push(`/plant/${latinNameUrl}`)
}
</script>

<template>
  <div class="max-w-[1200px] mx-auto lg:px-30 py-2">
    <!-- 统计信息 -->
    <div class="mb-20">
      <div
        :style="{
          backgroundImage: `url(${taxonomyBg})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }"
        class="h-40 w-full rounded-lg"
      ></div>

      <div class="text-center text-gray-700 mt-8">
        本站已收录
        <span class="font-bold text-primary-600">{{ taxonomyStore.families.length }}</span> 科、
        <span class="font-bold text-primary-600">{{ taxonomyStore.genusCount }}</span> 属中的共
        <span class="font-bold text-primary-600">{{ taxonomyStore.speciesCount }}</span>
        种热门植物。 如果你喜欢植物也喜欢本站，愿意跟我一起共建中文最大的植物百科全书，欢迎与我<span
          class="font-medium"
          ><a href="#">取得联系</a></span
        >
      </div>
      <!-- <div class="pattern-dots-sm slategray h-5"></div> -->
    </div>
    <!-- 分类列表组件 -->
    <TaxonomyList textSize="text-md" @species-click="handleSpeciesClick" />
  </div>
</template>

<style scoped>
@import '@/assets/pattern.min.css';
</style>
