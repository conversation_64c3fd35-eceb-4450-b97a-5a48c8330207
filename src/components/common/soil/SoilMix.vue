<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

/**
 * 土壤组件项接口
 */
interface SoilItem {
  name: string // 土壤名称
  percentage: number // 百分比
  image?: string // 可选的自定义图片路径
}

/**
 * 范围模型接口，用于表示pH值等范围数据
 */
interface RangeModel {
  min: number | null
  max: number | null
}

/**
 * 组件属性接口
 */
interface Props {
  soils: SoilItem[] // 土壤组件列表
  soil_ph?: RangeModel // 土壤酸碱度范围
  soil_notes?: string // 土壤相关注意事项
}

const props = defineProps<Props>()

/**
 * 土壤类型到图片URL的映射
 */
const SOIL_IMAGES: Record<string, string> = {
  泥炭土: new URL('./images/peat_soil.png', import.meta.url).href,
  珍珠岩: new URL('./images/perlite.png', import.meta.url).href,
  椰糠: new URL('./images/coco_coir.png', import.meta.url).href,
  水苔: new URL('./images/sphagnum_moss.png', import.meta.url).href,
  赤玉土: new URL('./images/akadama.png', import.meta.url).href,
  蛭石: new URL('./images/vermiculite.png', import.meta.url).href,
  火山岩: new URL('./images/lava_rock.png', import.meta.url).href,
  浮石: new URL('./images/pumice.png', import.meta.url).href,
  松树皮: new URL('./images/pine_bark.png', import.meta.url).href,
  鹿沼土: new URL('./images/kanuma.png', import.meta.url).href,
  硅藻土: new URL('./images/diatomitel.png', import.meta.url).href,
  园艺竹炭: new URL('./images/charcoal.png', import.meta.url).href,
  草木灰: new URL('./images/plants_ash.png', import.meta.url).href,
  腐殖土: new URL('./images/humus.png', import.meta.url).href,
  日向石: new URL('./images/hinohara.png', import.meta.url).href,
  麦饭石: new URL('./images/misoshi.png', import.meta.url).href,
  桐生砂: new URL('./images/takash.png', import.meta.url).href,
  陶粒: new URL('./images/ceramsite.png', import.meta.url).href,
  沸石: new URL('./images/zeolite.png', import.meta.url).href,
}

/**
 * 获取土壤图片URL
 * @param soil 土壤项
 * @returns 图片URL
 */
const getSoilImage = (soil: SoilItem): string => {
  // 如果提供了自定义图片，直接使用
  if (soil.image) return soil.image

  // 直接查找中文名称对应的图片
  return SOIL_IMAGES[soil.name] || ''
}

/**
 * 按百分比从高到低排序的土壤组件列表
 */
const sortedSoils = computed(() => {
  if (!props.soils || props.soils.length === 0) return []
  return [...props.soils].sort((a, b) => b.percentage - a.percentage)
})

/**
 * 格式化pH值显示
 */
const formattedPh = computed(() => {
  if (!props.soil_ph) return null

  const { min, max } = props.soil_ph
  if (min === null && max === null) return null

  if (min !== null && max !== null) {
    return `${min}~${max}`
  } else if (min !== null) {
    return `≥ ${min}`
  } else if (max !== null) {
    return `≤ ${max}`
  }

  return null
})

/**
 * 获取酸碱性描述
 */
const acidityType = computed(() => {
  if (!props.soil_ph?.min || !props.soil_ph?.max) return null

  const avgPh = (props.soil_ph.min + props.soil_ph.max) / 2

  if (avgPh < 3) return '强酸性'
  if (avgPh < 5) return '酸性'
  if (avgPh < 6.5) return '弱酸性'
  if (avgPh < 7.5) return '中性'
  if (avgPh < 8.5) return '弱碱性'
  if (avgPh < 10) return '碱性'
  return '强碱性'
})
</script>

<template>
  <div class="w-full">
    <!-- 土壤组成列表 -->
    <div class="flex flex-wrap gap-4">
      <div v-for="(soil, index) in sortedSoils" :key="index" class="flex items-center">
        <div class="w-10 h-10 rounded-full overflow-hidden mr-2">
          <img :src="getSoilImage(soil)" :alt="soil.name" class="w-full h-full object-cover" />
        </div>
        <div class="flex flex-col items-left">
          <div class="w-10 text-left text-[10px] font-bold">{{ soil.name }}</div>
          <div class="w-10 text-left text-sm font-extrabold">
            {{ soil.percentage }}<span class="text-[10px] px-0.5 font-normal">%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 土壤 pH 值 -->
    <div v-if="formattedPh && acidityType" class="mt-4 flex items-center gap-2 text-gray-500">
      <Icon icon="lucide:info" width="16" height="16" />
      <span class="text-xs italic"
        >{{ soil_notes }}。土壤需呈{{ acidityType }}，适宜pH值为{{ formattedPh }}</span
      >
    </div>
  </div>
</template>

<style scoped></style>
