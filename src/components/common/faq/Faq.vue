<script setup lang="ts">
import { ref, computed } from 'vue'
import { Icon } from '@iconify/vue'

// FAQ 数据结构
interface FaqLink {
  name: string
  url: string
}

interface FaqSection {
  title: string
  content: string
  links?: FaqLink
}

interface FaqData {
  [key: string]: FaqSection
}

// FAQ 数据示例
const defaultFaqs: FaqData = {
  section1: {
    title: '关于浇水常识，什么是「见干见湿」和「干透浇透」？',
    content:
      '「见干见湿」是指土壤表面干了就浇水，浇到表面湿润为止的浇水方法。适用于喜湿植物或生长期需水量大的植物。「干透浇透」是指等土壤完全干透后，再彻底浇透的方法。适用于多肉、仙人掌等耐旱植物，可以避免根部长期处于潮湿环境而腐烂。选择哪种浇水方式应根据植物种类、生长阶段和环境条件来决定。',
    links: { name: '阅读更多', url: '#' },
  },
  section2: {
    title: '配土攻略和原则',
    content:
      '配土有三个原则：\n第一：透气性，要让植物的根系也能吸收空气，并且疏松透气的土壤才更能让水分渗透，不容易造成积水烂根\n第二：注意土壤的营养性，土壤肥力大于才能长势更好\n第三：注意植物的特性，不同的植物对土壤酸碱度有不同的需求，像杜鹃花就喜欢酸性土壤，而铁线莲就喜欢弱碱性土壤。而且不同的植物对于肥度和水分需求也不同，根据他们的需求进行配土，才能让植物长得更好哦',
    links: { name: '扩展阅读', url: '#' },
  },
  section3: {
    title: '室内植物的正确摆放位置以及如何判断直射光、散射光等？',
    content:
      '室内植物摆放位置应考虑光照需求、温度和湿度条件。直射光：阳光直接照射到植物上，通常出现在南向或西向窗户附近，光线强烈且有明显的光斑。适合仙人掌、多肉等耐旱植物。散射光：阳光经过窗帘或其他障碍物过滤后的柔和光线，没有明显的光斑，亮度适中。适合大多数室内观叶植物如绿萝、常春藤等。弱光区：远离窗户的区域，光线较暗。适合喜阴植物如蕨类、龟背竹等。判断方法：在不同位置放一张白纸，观察光线在纸上形成的阴影清晰度和亮度，或使用手机光照测量应用。',
    links: { name: '查看详情', url: '#' },
  },
  section4: {
    title: '植物在不同生长阶段的施肥指南？',
    content:
      '植物施肥应根据生长阶段调整：休眠期：大多数植物在冬季或特定季节会进入休眠状态，此时应停止施肥或极少量施肥，避免肥害。生长期：这是植物快速生长的阶段，需要充足的养分。可每2-4周施用一次稀释的液体肥料，氮肥为主促进叶片生长。开花期：应增加磷钾肥的比例，减少氮肥，促进花芽分化和花朵形成。结果期：需要均衡的肥料配比，特别是钾肥，以促进果实发育。记住"薄肥勤施"原则，宁可少施也不要过量，并根据植物反应调整施肥计划。',
    links: { name: '施肥详解', url: '#' },
  },
  section5: {
    title: '散射光和直射光怎么区别？光照指南',
    content:
      '区分散射光和直射光的方法：\n直射光：\n- 阳光直接照射到植物上，没有遮挡物\n- 在地面或墙壁上形成清晰的光斑和阴影\n- 通常出现在南向或西向窗户附近\n- 光线强烈，手放在植物位置能感受到明显的热度\n- 适合仙人掌、多肉、向日葵等喜阳植物\n\n散射光：\n- 阳光经过窗帘、纱窗或其他障碍物过滤后的光线\n- 光线柔和均匀，没有明显的光斑\n- 亮度适中，不会灼伤植物叶片\n- 适合大多数室内观叶植物如绿萝、常春藤、蕨类等\n\n简易测试方法：在植物摆放位置举起手，观察手影的清晰度。清晰的手影表示直射光，模糊的手影表示散射光。',
    links: { name: '光照详解', url: '#' },
  },
}

// 可以通过 props 传入自定义 FAQ 数据，如果不传则使用默认数据
const props = defineProps<{
  faqs?: FaqData
}>()

// 使用传入的 faqs 或默认的 defaultFaqs
const faqData = computed(() => props.faqs || defaultFaqs)

// 当前展开的部分
const activeSection = ref<string | null>(null)

// 切换展开/折叠状态
const toggleSection = (sectionKey: string) => {
  if (activeSection.value === sectionKey) {
    // 如果点击的是当前已展开的部分，则折叠
    activeSection.value = null
  } else {
    // 否则展开点击的部分，并折叠其他部分
    activeSection.value = sectionKey
  }
}

// 检查部分是否展开
const isSectionActive = (sectionKey: string): boolean => {
  return activeSection.value === sectionKey
}

// 获取内容高度
const getContentHeight = (key: string) => {
  const isActive = isSectionActive(key)
  return isActive ? 'auto' : '0px'
}
</script>

<template>
  <div class="w-full">
    <!-- FAQ 项目列表 -->
    <div class="space-y-4">
      <div
        v-for="(section, key) in faqData"
        :key="key"
        class="faq-item border-b border-gray-100 py-4"
      >
        <div class="flex items-start">
          <!-- 问题 ID -->
          <div
            class="text-2xl font-medium text-gray-200 mr-4 lg:mr-10 w-8 text-center pt-4 self-start"
          >
            {{ key.replace('section', '') }}
          </div>

          <div class="flex-1">
            <!-- FAQ 标题行 -->
            <div
              class="faq-header flex items-center justify-between py-4 cursor-pointer"
              @click="toggleSection(key)"
            >
              <h3 class="text-xl font-medium tracking-wider">{{ section.title }}</h3>
              <div
                :class="[
                  'w-8 h-8 p-2 rounded-full flex items-center justify-center transition-all duration-100',
                  isSectionActive(key) ? 'bg-black text-white' : 'bg-gray-50 text-gray-500',
                ]"
              >
                <Icon
                  :icon="isSectionActive(key) ? 'mingcute:close-line' : 'mingcute:add-line'"
                  class="text-2xl transition-all duration-200"
                />
              </div>
            </div>

            <!-- FAQ 内容区域 -->
            <div
              class="faq-content-wrapper tracking-wider overflow-hidden transition-all duration-200 ease-in-out"
              :style="{
                maxHeight: isSectionActive(key) ? '1000px' : '0px',
                opacity: isSectionActive(key) ? 1 : 0,
                transform: isSectionActive(key) ? 'translateY(0)' : 'translateY(-10px)',
              }"
            >
              <div class="pt-2 pb-4 text-gray-600 pr-12">
                <p class="whitespace-pre-line">{{ section.content }}</p>

                <!-- 链接区域 -->
                <div v-if="section.links" class="mt-4">
                  <a
                    :href="section.links.url"
                    class="text-primary-900 font-medium hover:underline inline-flex items-center"
                  >
                    {{ section.links.name }}
                    <Icon icon="iconamoon:arrow-right-2-bold" class="ml-1" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.faq-content-wrapper {
  will-change: max-height, opacity, transform;
  transform-origin: top;
}

.faq-item:not(:last-child) {
  margin-bottom: 0.5rem;
}
</style>
