<template>
  <div class="w-full flex flex-wrap gap-3 text-sm">
    <div
      v-for="(tag, index) in tags"
      :key="index"
      class="px-3 py-1 rounded-md bg-white shadow-xs border-1 border-gray-200 text-gray-600 flex items-center whitespace-nowrap"
    >
      <span v-if="tag.name" class="text-gray-400 mr-1 font-medium">{{ tag.name }}:</span>
      <span class="text-gray-700 font-medium">{{ tag.value }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, withDefaults } from 'vue'

// 定义 Props 接口
interface Tag {
  value: string
  name?: string
}

interface Props {
  tags: Tag[]
}

// 使用 withDefaults 设置默认值
withDefaults(defineProps<Props>(), {
  tags: () => [],
})
</script>

<style scoped></style>
