<script setup lang="ts">
import { computed, defineProps, ref, withDefaults } from 'vue'
import { Icon } from '@iconify/vue'
// import { vTooltip } from 'floating-vue'

interface Range {
  min: number
  max: number
}

// 定义 Props 接口
interface Props {
  name: 'temperature' | 'light' | 'humidity'
  range: Range
  extreme: Range
  ruler?: Range
  theme?: string
}

// 使用 withDefaults 设置默认值
const props = withDefaults(defineProps<Props>(), {
  ruler: (props) => {
    switch (props.name) {
      case 'temperature':
        return { min: -5, max: 45 }
      case 'light':
        return { min: 500, max: 20000 }
      case 'humidity':
        return { min: 0, max: 100 }
      default:
        return { min: 0, max: 100 }
    }
  },
  theme: (props) => {
    switch (props.name) {
      case 'temperature':
        return 'blue'
      case 'light':
        return 'yellow'
      case 'humidity':
        return 'green'
      default:
        return 'blue'
    }
  },
})

// 计算标题和单位
const title = computed(() => {
  switch (props.name) {
    case 'temperature':
      return '温度参考 (°C)'
    case 'light':
      return '光照度参考 (lux)'
    case 'humidity':
      return '湿度参考 (%)'
    default:
      return ''
  }
})

// 计算图标
const icon = computed(() => {
  switch (props.name) {
    case 'temperature':
      return 'mdi:temperature-lines'
    case 'light':
      return 'mingcute:sun-fill'
    case 'humidity':
      return 'material-symbols:humidity-percentage-rounded'
    default:
      return ''
  }
})

// 计算主题颜色
const themeColor = computed(() => {
  // 如果是预定义的颜色，返回对应的颜色值
  if (props.theme === 'blue') return '#1a73e8'
  if (props.theme === 'yellow') return '#fbbc04'
  if (props.theme === 'green') return '#34a853'

  // 如果是自定义颜色值，直接返回
  return props.theme
})

// 计算最佳范围的位置百分比
const rangePosition = computed(() => {
  const { min, max } = props.ruler
  const { min: rangeMin, max: rangeMax } = props.range
  const totalWidth = max - min

  return {
    left: ((rangeMin - min) / totalWidth) * 100,
    width: ((rangeMax - rangeMin) / totalWidth) * 100,
  }
})

// 计算极限范围的位置百分比
const extremePosition = computed(() => {
  const { min, max } = props.ruler
  const { min: extremeMin, max: extremeMax } = props.extreme
  const totalWidth = max - min

  return {
    left: ((extremeMin - min) / totalWidth) * 100,
    width: ((extremeMax - extremeMin) / totalWidth) * 100,
  }
})

// 计算刻度值
const rulerValues = computed(() => {
  const { min, max } = props.ruler
  const { min: rangeMin, max: rangeMax } = props.range
  const { min: extremeMin, max: extremeMax } = props.extreme

  // 返回需要显示的关键值，去除重复值
  const values = Array.from(new Set([min, extremeMin, rangeMin, rangeMax, extremeMax, max]))
  return values.sort((a, b) => a - b)
})

// 获取值的类型（用于设置不同的颜色）
const getValueType = (value: number) => {
  // const [min, max] = props.ruler
  const { min: rangeMin, max: rangeMax } = props.range
  const { min: extremeMin, max: extremeMax } = props.extreme

  if (value === rangeMin || value === rangeMax) {
    return 'range'
  } else if (value === extremeMin || value === extremeMax) {
    return 'extreme'
  } else {
    return 'ruler'
  }
}

// 获取值的位置百分比
const getValuePosition = (value: number) => {
  const { min, max } = props.ruler
  const totalWidth = max - min

  return ((value - min) / totalWidth) * 100
}

// 分别为最佳范围和极限范围创建独立的悬停状态
const isRangeHovered = ref(false)
const isExtremeHovered = ref(false)

// 最佳范围悬停处理函数
const handleRangeMouseEnter = () => {
  // 如果鼠标从极限范围移动到最佳范围，取消极限范围的悬停状态
  if (isExtremeHovered.value) {
    isExtremeHovered.value = false
  }
  isRangeHovered.value = true
}

const handleRangeMouseLeave = () => {
  isRangeHovered.value = false
}

// 极限范围悬停处理函数
const handleExtremeMouseEnter = () => {
  // 只有当最佳范围没有被悬停时，才设置极限范围的悬停状态
  if (!isRangeHovered.value) {
    isExtremeHovered.value = true
  }
}

const handleExtremeMouseLeave = () => {
  isExtremeHovered.value = false
}

// 判断刻度值是否应该高亮显示
const shouldHighlightValue = (value: number) => {
  if (isRangeHovered.value && getValueType(value) === 'range') return true
  if (isExtremeHovered.value && getValueType(value) === 'extreme') return true
  return false
}

// 判断刻度值是否应该隐藏
const shouldHideValue = (value: number) => {
  // 如果没有悬停，不隐藏任何值
  if (!isRangeHovered.value && !isExtremeHovered.value) return false

  // 如果悬停在最佳范围，只显示最佳范围的值
  if (isRangeHovered.value) return getValueType(value) !== 'range'

  // 如果悬停在极限范围，只显示极限范围的值
  if (isExtremeHovered.value) return getValueType(value) !== 'extreme'

  return false
}
</script>

<template>
  <div class="w-full mb-8">
    <div class="flex items-center mb-6">
      <Icon :icon="icon" :style="{ color: themeColor }" class="mr-2" width="20" height="20" />
      <span class="text-xs font-medium">{{ title }}</span>
    </div>

    <div class="relative cursor-pointer px-2">
      <!-- 刻度值区域 - 显示数值和刻度线 -->
      <div class="relative h-2 mb-1">
        <div
          v-for="(value, index) in rulerValues"
          :key="index"
          class="absolute transform -translate-x-1/2 whitespace-nowrap bottom-0 transition-all duration-300"
          :style="{ left: `${getValuePosition(value)}%` }"
          :class="{
            'scale-110 z-10': shouldHighlightValue(value),
            'opacity-0': shouldHideValue(value),
          }"
        >
          <!-- 刻度值文本 - 根据类型和悬停状态改变样式 -->
          <span
            class="text-xs transition-all duration-300"
            :class="{
              'text-gray-600 font-medium': getValueType(value) === 'range',
              'text-gray-400': getValueType(value) === 'extreme',
              'text-gray-200': getValueType(value) === 'ruler',
              'font-medium': shouldHighlightValue(value),
            }"
          >
            {{ value }}
          </span>
          <!-- 刻度线 -->
          <div class="h-[2px] w-[1px] bg-gray-300 mx-auto mb-1"></div>
        </div>
      </div>

      <!-- 刻度尺背景 - 包含最佳范围和极限范围 -->
      <div class="h-2 bg-gray-100 rounded-full relative overflow-visible">
        <!-- 极限范围区域 - 悬停时放大并提高层级 -->
        <div
          class="absolute h-full rounded-full transition-all duration-300 cursor-pointer"
          :class="{
            'z-20': isExtremeHovered && !isRangeHovered,
            'pointer-events-none': isRangeHovered,
          }"
          :style="{
            left: `${extremePosition.left}%`,
            width: `${extremePosition.width}%`,
            backgroundColor: themeColor,
            transform: isExtremeHovered ? 'scaleY(1.5) scaleX(1.05)' : 'scale(1)',
            opacity: isExtremeHovered ? '0.4' : '0.15',
          }"
          @mouseenter="handleExtremeMouseEnter"
          @mouseleave="handleExtremeMouseLeave"
        ></div>

        <!-- 最佳范围区域 - 悬停时放大，优先级高于极限范围 -->
        <div
          class="absolute h-full rounded-full transition-all duration-300 cursor-pointer z-30"
          :style="{
            left: `${rangePosition.left}%`,
            width: `${rangePosition.width}%`,
            backgroundColor: themeColor,
            transform: isRangeHovered ? 'scaleY(1.5) scaleX(1.05)' : 'scale(1)',
            opacity: isRangeHovered ? '1' : isExtremeHovered ? '0.7' : '1',
          }"
          @mouseenter="handleRangeMouseEnter"
          @mouseleave="handleRangeMouseLeave"
        ></div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
