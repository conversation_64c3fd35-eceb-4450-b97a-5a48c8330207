<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  content: string // tooltip 显示的文本内容
  followMouse?: boolean // 是否跟随鼠标移动，默认为 false
  placement?: 'top' | 'right' | 'bottom' | 'left' // 箭头位置，默认为 bottom
  offset?: number // 与触发元素的距离，默认为 10px
  delay?: number // 显示延迟，单位毫秒，默认为 0
  width?: string // tooltip 宽度，默认为 auto
  dark?: boolean // 是否使用深色主题，默认为 true
}

const props = withDefaults(defineProps<Props>(), {
  followMouse: false,
  placement: 'bottom',
  offset: 10,
  delay: 0,
  width: 'auto',
  dark: true,
})

// 定义事件
const emit = defineEmits<{
  (e: 'show'): void
  (e: 'hide'): void
}>()

// 引用元素
const triggerRef = ref<HTMLElement | null>(null)
const tooltipRef = ref<HTMLElement | null>(null)

// 状态
const isVisible = ref(false)
const position = ref({ x: 0, y: 0 })
const delayTimer = ref<number | null>(null)

// 计算样式
const tooltipStyle = computed(() => {
  const style: Record<string, string> = {
    width: props.width,
  }

  if (props.followMouse) {
    style.left = `${position.value.x}px`
    style.top = `${position.value.y}px`
    style.transform = getTransformByPlacement(props.placement)
  }

  return style
})

// 计算类名
const tooltipClasses = computed(() => {
  const classes = ['tooltip-container']

  if (isVisible.value) {
    classes.push('visible')
  }

  if (props.dark) {
    classes.push('dark')
  } else {
    classes.push('light')
  }

  if (!props.followMouse) {
    classes.push(`placement-${props.placement}`)
  }

  return classes.join(' ')
})

// 根据位置计算transform
const getTransformByPlacement = (placement: string): string => {
  switch (placement) {
    case 'top':
      return 'translate(-50%, -100%)'
    case 'right':
      return 'translateY(-50%)'
    case 'bottom':
      return 'translate(-50%, 10px)'
    case 'left':
      return 'translate(-100%, -50%)'
    default:
      return 'translate(-50%, 10px)'
  }
}

// 计算静态位置
const calculatePosition = () => {
  if (!triggerRef.value || !tooltipRef.value || props.followMouse) return

  const triggerRect = triggerRef.value.getBoundingClientRect()
  const tooltipRect = tooltipRef.value.getBoundingClientRect()

  let x = 0
  let y = 0

  switch (props.placement) {
    case 'top':
      x = triggerRect.left + triggerRect.width / 2
      y = triggerRect.top - props.offset
      break
    case 'right':
      x = triggerRect.right + props.offset
      y = triggerRect.top + triggerRect.height / 2
      break
    case 'bottom':
      x = triggerRect.left + triggerRect.width / 2
      y = triggerRect.bottom + props.offset
      break
    case 'left':
      x = triggerRect.left - props.offset
      y = triggerRect.top + triggerRect.height / 2
      break
  }

  position.value = { x, y }
}

// 鼠标移动处理函数
const handleMouseMove = (event: MouseEvent) => {
  if (!props.followMouse || !isVisible.value) return

  // 根据箭头位置调整鼠标跟随位置
  let x = event.clientX
  let y = event.clientY

  switch (props.placement) {
    case 'top':
      y -= props.offset
      break
    case 'right':
      x += props.offset
      break
    case 'bottom':
      y += props.offset
      break
    case 'left':
      x -= props.offset
      break
  }

  position.value = { x, y }
}

// 显示 tooltip
const showTooltip = () => {
  if (delayTimer.value) {
    clearTimeout(delayTimer.value)
    delayTimer.value = null
  }

  if (props.delay > 0) {
    delayTimer.value = window.setTimeout(() => {
      isVisible.value = true
      calculatePosition()
      emit('show')
    }, props.delay)
  } else {
    isVisible.value = true
    calculatePosition()
    emit('show')
  }
}

// 隐藏 tooltip
const hideTooltip = () => {
  if (delayTimer.value) {
    clearTimeout(delayTimer.value)
    delayTimer.value = null
  }

  isVisible.value = false
  emit('hide')
}

// 暴露方法给父组件
defineExpose({
  show: showTooltip,
  hide: hideTooltip,
})

// 生命周期钩子
onMounted(() => {
  // 获取默认插槽的第一个元素作为触发元素
  triggerRef.value = document.querySelector('.tooltip-trigger')

  if (triggerRef.value) {
    triggerRef.value.addEventListener('mouseenter', showTooltip)
    triggerRef.value.addEventListener('mouseleave', hideTooltip)
  }

  if (props.followMouse) {
    document.addEventListener('mousemove', handleMouseMove)
  }
})

onUnmounted(() => {
  if (triggerRef.value) {
    triggerRef.value.removeEventListener('mouseenter', showTooltip)
    triggerRef.value.removeEventListener('mouseleave', hideTooltip)
  }

  if (props.followMouse) {
    document.removeEventListener('mousemove', handleMouseMove)
  }

  if (delayTimer.value) {
    clearTimeout(delayTimer.value)
  }
})
</script>

<template>
  <div class="tooltip-wrapper">
    <!-- 触发元素 -->
    <div class="tooltip-trigger">
      <slot></slot>
    </div>

    <!-- Tooltip 内容 -->
    <div ref="tooltipRef" :class="tooltipClasses" :style="tooltipStyle" v-show="isVisible">
      <div class="tooltip-content">{{ content }}</div>
      <div class="tooltip-arrow" v-if="!followMouse"></div>
    </div>
  </div>
</template>

<style scoped>
.tooltip-wrapper {
  position: relative;
  display: inline-block;
}

.tooltip-container {
  position: absolute;
  z-index: 9999;
  max-width: 300px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.4;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
}

.tooltip-container.visible {
  opacity: 1;
}

.tooltip-container.dark {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
}

.tooltip-container.light {
  background-color: white;
  color: #333;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

/* 箭头位置样式 */
.tooltip-container.placement-top {
  transform: translate(-50%, -100%);
}

.tooltip-container.placement-top .tooltip-arrow {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px 6px 0 6px;
  border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
}

.tooltip-container.placement-top.light .tooltip-arrow {
  border-color: #e2e8f0 transparent transparent transparent;
}

.tooltip-container.placement-right {
  transform: translateY(-50%);
}

.tooltip-container.placement-right .tooltip-arrow {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 6px 6px 6px 0;
  border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent;
}

.tooltip-container.placement-right.light .tooltip-arrow {
  border-color: transparent #e2e8f0 transparent transparent;
}

.tooltip-container.placement-bottom {
  transform: translate(-50%, 10px);
}

.tooltip-container.placement-bottom .tooltip-arrow {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 6px 6px 6px;
  border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
}

.tooltip-container.placement-bottom.light .tooltip-arrow {
  border-color: transparent transparent #e2e8f0 transparent;
}

.tooltip-container.placement-left {
  transform: translate(-100%, -50%);
}

.tooltip-container.placement-left .tooltip-arrow {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 6px 0 6px 6px;
  border-color: transparent transparent transparent rgba(0, 0, 0, 0.8);
}

.tooltip-container.placement-left.light .tooltip-arrow {
  border-color: transparent transparent transparent #e2e8f0;
}
</style>
