<script setup lang="ts">
import { computed, withDefaults } from 'vue'

// ===================== 类型定义区域 =====================
// 定义 Props 接口
interface Props {
  // PH 值范围
  min: number
  max: number
  // 整个 PH 刻度范围
  phMin?: number
  phMax?: number
}

// ===================== Props 和默认值 =====================
const props = withDefaults(defineProps<Props>(), {
  phMin: 4,
  phMax: 10,
})

// ===================== 计算属性区域 =====================
// 计算 PH 值条的总宽度
const phRange = computed(() => props.phMax - props.phMin)

// 计算滑块的位置和宽度
const sliderStyle = computed(() => {
  // 计算最小值和最大值相对于整个范围的百分比位置
  const minPercent = ((props.min - props.phMin) / phRange.value) * 100
  const maxPercent = ((props.max - props.phMin) / phRange.value) * 100

  return {
    left: `${minPercent}%`,
    width: `${maxPercent - minPercent}%`,
  }
})

// 获取滑块的渐变色
const sliderGradient = computed(() => {
  // 获取最小值和最大值对应的颜色
  const minColor = exactColorMap[Math.floor(props.min) as keyof typeof exactColorMap] || '#B7CB86'
  const maxColor = exactColorMap[Math.ceil(props.max) as keyof typeof exactColorMap] || '#B7CB86'

  return `linear-gradient(to right, ${minColor}, ${maxColor})`
})

// 使用图片中提供的精确颜色代码
const exactColorMap = {
  // 从图片中提取的颜色
  0: '#811E19', // 深红色
  1: '#9F3A28', // 红色
  2: '#DF5D45', // 橙红色
  3: '#E28F52', // 橙色
  4: '#F3B55B', // 橙黄色
  5: '#FFC758', // 黄色
  6: '#FFDF58', // 浅黄色
  7: '#DCD472', // 淡黄绿色
  8: '#B7CB86', // 浅绿色
  9: '#92BC98', // 绿色
  10: '#4DABB7', // 青色
  11: '#3A939B', // 深青色
  12: '#357179', // 更深青色
  13: '#298789', // 深蓝绿色
  14: '#23767A', // 最深蓝绿色
}

// 获取刻度值数组
const ticks = computed(() => {
  const result = []
  for (let i = props.phMin; i <= props.phMax; i++) {
    result.push(i)
  }
  return result
})

// 获取刻度标签
const tickLabels = computed(() => {
  // 只在起始、中间和结束位置显示标签
  const labels: Record<number, string> = {}

  // 起始位置
  labels[props.phMin] = props.phMin.toString()

  // 中间位置 - 7 是中性
  if (props.phMin <= 7 && props.phMax >= 7) {
    labels[7] = '7'
  }

  // 结束位置
  labels[props.phMax] = props.phMax.toString()

  return labels
})

// 获取描述标签
const descriptionLabels = computed(() => {
  const labels: Record<number, string> = {}

  // 强酸标签
  if (props.phMin <= 4) {
    labels[4] = '酸性'
  }

  // 中性标签
  if (props.phMin <= 7 && props.phMax >= 7) {
    labels[7] = '中性'
  }

  // 强碱标签
  if (props.phMax >= 10) {
    labels[10] = '碱性'
  }

  return labels
})

// 获取酸碱性描述
const acidityType = computed(() => {
  const avgPh = (props.min + props.max) / 2

  if (avgPh < 3) return '强酸性'
  if (avgPh < 5) return '酸性'
  if (avgPh < 6.5) return '弱酸性'
  if (avgPh < 7.5) return '中性'
  if (avgPh < 8.5) return '弱碱性'
  if (avgPh < 10) return '碱性'
  return '强碱性'
})
</script>

<template>
  <div class="w-full mx-auto mt-2 mb-4">
    <!-- 顶部区域：滑块指示器和标题 -->
    <div class="flex items-center mb-1">
      <!-- 滑块区域 - 左侧 -->
      <div class="flex-grow relative h-8 cursor-pointer">
        <!-- 滑块背景 -->
        <div
          class="absolute top-1/2 transform -translate-y-1/2 h-5 rounded-full border-2 border-gray-800"
          :style="{
            left: sliderStyle.left,
            width: sliderStyle.width,
            background: sliderGradient,
          }"
        ></div>

        <!-- 滑块两侧的 PH 值 -->
        <div
          class="absolute top-1/2 transform -translate-y-1/2 text-sm font-bold"
          :style="{ left: `calc(${sliderStyle.left} - 30px)` }"
        >
          {{ props.min.toFixed(1) }}
        </div>

        <div
          class="absolute top-1/2 transform -translate-y-1/2 text-sm font-bold"
          :style="{ left: `calc(${sliderStyle.left} + ${sliderStyle.width} + 10px)` }"
        >
          {{ props.max.toFixed(1) }}
        </div>
      </div>

      <!-- 右侧标题 -->
      <div class="ml-4 text-right flex flex-col justify-center">
        <div class="text-[10px] font-medium text-gray-600 leading-tight">
          土壤需呈{{ acidityType }}
        </div>
        <div class="text-[10px] font-medium text-gray-600 leading-tight -mt-0.5">
          pH {{ props.min.toFixed(1) }}~{{ props.max.toFixed(1) }}
        </div>
      </div>
    </div>

    <!-- PH 值条 -->
    <div class="relative h-5">
      <!-- 彩色背景条 -->
      <div class="absolute inset-0 flex cursor-pointer">
        <div
          v-for="tick in ticks"
          :key="tick"
          class="h-full flex-1 relative"
          :style="{
            backgroundColor: exactColorMap[tick as keyof typeof exactColorMap] || '#B7CB86',
          }"
        >
          <!-- 每格内的 PH 值数字 - 垂直居中 -->
          <span
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs text-white font-medium"
          >
            {{ tick }}
          </span>
        </div>
      </div>
    </div>

    <!-- 酸碱度 pH 描述 -->
    <div class="flex mt-1">
      <div v-for="tick in ticks" :key="tick" class="flex-1 text-center">
        <span v-if="descriptionLabels[tick]" class="text-xs font-medium text-gray-400">
          {{ descriptionLabels[tick] }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
