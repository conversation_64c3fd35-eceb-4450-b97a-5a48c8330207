<script setup lang="ts">
import { computed, withDefaults } from 'vue'

// ===================== 类型定义区域 =====================
// 定义 Props 接口 - 描述组件对外暴露的所有属性
interface Props {
  // 总页数
  totalPages: number
  // 当前页码
  currentPage: number
  // 可选属性 - 提供合理的默认值
  disabled?: boolean
  // 是否显示首页和末页按钮
  showEndButtons?: boolean
  // 固定显示的页码按钮数量（不包括上一页、下一页按钮）
  visiblePageCount?: number
}

// ===================== Props 和默认值 =====================
// 使用 withDefaults 设置智能默认值
const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showEndButtons: false,
  visiblePageCount: 7, // 默认显示7个元素（包括可能的省略号）
})

// ===================== 事件定义 =====================
const emit = defineEmits<{
  (e: 'update:currentPage', page: number): void
}>()

// ===================== 计算属性区域 =====================
/**
 * 计算需要显示的页码数组
 * 规则：
 * 1. 总是显示固定数量的元素（visiblePageCount）
 * 2. 始终显示第一页和最后一页
 * 3. 当前页附近的页码优先显示
 * 4. 使用省略号填充剩余位置，保持总元素数量不变
 */
const pageNumbers = computed(() => {
  const { totalPages, currentPage, visiblePageCount } = props

  // 如果总页数小于等于可见页码数，直接返回所有页码
  if (totalPages <= visiblePageCount) {
    return Array.from({ length: totalPages }, (_, i) => i + 1)
  }

  // 否则，需要计算显示哪些页码和省略号
  const result: (number | string)[] = []

  // 计算需要显示的页码数（不包括省略号）
  const pageNumbersToShow = visiblePageCount - 2 // 减去可能的两个省略号位置

  // 始终显示第一页
  result.push(1)

  // 计算中间页码的起始和结束位置
  let startPage: number
  let endPage: number

  // 当前页靠近开始位置
  if (currentPage <= Math.ceil(pageNumbersToShow / 2)) {
    startPage = 2
    endPage = pageNumbersToShow

    // 添加中间页码
    for (let i = startPage; i <= endPage && i < totalPages; i++) {
      result.push(i)
    }

    // 添加省略号和最后一页
    if (endPage + 1 < totalPages) {
      result.push('...')
    }
    result.push(totalPages)

    // 如果元素不足，用空白填充（保持固定数量）
    while (result.length < visiblePageCount) {
      result.push('')
    }
  }
  // 当前页靠近结束位置
  else if (currentPage > totalPages - Math.ceil(pageNumbersToShow / 2)) {
    endPage = totalPages - 1
    startPage = endPage - pageNumbersToShow + 2

    // 添加省略号
    if (startPage > 2) {
      result.push('...')
    }

    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }

    // 添加最后一页
    result.push(totalPages)

    // 如果元素不足，用空白填充（保持固定数量）
    while (result.length < visiblePageCount) {
      result.push('')
    }
  }
  // 当前页在中间位置
  else {
    const offset = Math.floor(pageNumbersToShow / 2) - 1
    startPage = currentPage - offset
    endPage = currentPage + offset

    // 确保不超出范围
    if (endPage >= totalPages) {
      endPage = totalPages - 1
      startPage = endPage - pageNumbersToShow + 1
    }

    // 添加左侧省略号
    if (startPage > 2) {
      result.push('...')
    } else {
      // 如果没有左侧省略号，则显示第2页
      result.push(2)
    }

    // 添加中间页码
    for (let i = startPage + 1; i < endPage; i++) {
      result.push(i)
    }

    // 添加右侧省略号
    if (endPage < totalPages - 1) {
      result.push('...')
    } else {
      // 如果没有右侧省略号，则显示倒数第二页
      result.push(totalPages - 1)
    }

    // 添加最后一页
    result.push(totalPages)

    // 如果元素不足，用空白填充（保持固定数量）
    while (result.length < visiblePageCount) {
      result.push('')
    }
  }

  // 过滤掉空字符串，保留数字和省略号
  return result.filter((item) => item !== '')
})

// 判断是否可以前进到上一页
const canGoPrevious = computed(() => props.currentPage > 1 && !props.disabled)

// 判断是否可以前进到下一页
const canGoNext = computed(() => props.currentPage < props.totalPages && !props.disabled)

// ===================== 事件处理函数区域 =====================
/**
 * 处理页码变更
 * @param page 目标页码
 */
const handlePageChange = (page: number | string) => {
  // 如果是省略号或者组件被禁用，不做任何操作
  if (page === '...' || props.disabled) return

  // 如果点击的是当前页，不做任何操作
  if (page === props.currentPage) return

  // 更新当前页码
  emit('update:currentPage', page as number)
}

/**
 * 跳转到上一页
 */
const goToPreviousPage = () => {
  if (canGoPrevious.value) {
    emit('update:currentPage', props.currentPage - 1)
  }
}

/**
 * 跳转到下一页
 */
const goToNextPage = () => {
  if (canGoNext.value) {
    emit('update:currentPage', props.currentPage + 1)
  }
}

/**
 * 跳转到第一页
 */
const goToFirstPage = () => {
  if (props.currentPage !== 1 && !props.disabled) {
    emit('update:currentPage', 1)
  }
}

/**
 * 跳转到最后一页
 */
const goToLastPage = () => {
  if (props.currentPage !== props.totalPages && !props.disabled) {
    emit('update:currentPage', props.totalPages)
  }
}
</script>

<template>
  <div class="flex items-center justify-center space-x-1 select-none pt-10 pb-20">
    <!-- 首页按钮 -->
    <button
      v-if="showEndButtons"
      @click="goToFirstPage"
      :disabled="!canGoPrevious || disabled"
      class="px-2 py-1 rounded-md text-sm flex items-center justify-center"
      :class="[
        canGoPrevious && !disabled
          ? 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
          : 'text-gray-400 cursor-not-allowed dark:text-gray-600',
      ]"
      aria-label="首页"
    >
      <span class="sr-only">首页</span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414z"
          clip-rule="evenodd"
        />
        <path
          fill-rule="evenodd"
          d="M7.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L3.414 10l4.293 4.293a1 1 0 010 1.414z"
          clip-rule="evenodd"
        />
      </svg>
    </button>

    <!-- 上一页按钮 -->
    <button
      @click="goToPreviousPage"
      :disabled="!canGoPrevious || disabled"
      class="px-2 py-1 rounded-md text-sm flex items-center justify-center"
      :class="[
        canGoPrevious && !disabled
          ? 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
          : 'text-gray-400 cursor-not-allowed dark:text-gray-600',
      ]"
      aria-label="上一页"
    >
      <span class="sr-only">上一页</span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
          clip-rule="evenodd"
        />
      </svg>
    </button>

    <!-- 页码按钮 -->
    <div class="flex space-x-1">
      <button
        v-for="page in pageNumbers"
        :key="page"
        @click="handlePageChange(page)"
        :disabled="page === '...' || disabled"
        class="min-w-[32px] h-8 px-2 rounded-md text-sm flex items-center justify-center"
        :class="[
          page === currentPage
            ? 'bg-blue-500 text-white'
            : page === '...'
              ? 'text-gray-500 cursor-default dark:text-gray-400'
              : !disabled
                ? 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                : 'text-gray-400 cursor-not-allowed dark:text-gray-600',
        ]"
        :aria-current="page === currentPage ? 'page' : undefined"
        :aria-label="page === '...' ? '更多页码' : `第${page}页`"
      >
        {{ page }}
      </button>
    </div>

    <!-- 下一页按钮 -->
    <button
      @click="goToNextPage"
      :disabled="!canGoNext || disabled"
      class="px-2 py-1 rounded-md text-sm flex items-center justify-center"
      :class="[
        canGoNext && !disabled
          ? 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
          : 'text-gray-400 cursor-not-allowed dark:text-gray-600',
      ]"
      aria-label="下一页"
    >
      <span class="sr-only">下一页</span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
          clip-rule="evenodd"
        />
      </svg>
    </button>

    <!-- 末页按钮 -->
    <button
      v-if="showEndButtons"
      @click="goToLastPage"
      :disabled="!canGoNext || disabled"
      class="px-2 py-1 rounded-md text-sm flex items-center justify-center"
      :class="[
        canGoNext && !disabled
          ? 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
          : 'text-gray-400 cursor-not-allowed dark:text-gray-600',
      ]"
      aria-label="末页"
    >
      <span class="sr-only">末页</span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M4.293 15.707a1 1 0 001.414 0l5-5a1 1 0 000-1.414l-5-5a1 1 0 00-1.414 1.414L8.586 10l-4.293 4.293a1 1 0 000 1.414z"
          clip-rule="evenodd"
        />
        <path
          fill-rule="evenodd"
          d="M12.293 15.707a1 1 0 001.414 0l5-5a1 1 0 000-1.414l-5-5a1 1 0 00-1.414 1.414L16.586 10l-4.293 4.293a1 1 0 000 1.414z"
          clip-rule="evenodd"
        />
      </svg>
    </button>
  </div>
</template>
