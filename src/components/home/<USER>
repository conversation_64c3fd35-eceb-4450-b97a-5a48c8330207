<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { Icon } from '@iconify/vue'

// 导入图片资源
import heroBg1 from '@/assets/heroBg/hero_bg_1.jpg'
import heroBg2 from '@/assets/heroBg/hero_bg_2.jpg'

// 定义封面图片数据
const coverImages = [
  {
    id: 1,
    src: heroBg1,
    alt: '多肉植物',
    credit: '林子晨',
    creditTitle: '多肉收藏家',
    creditAvatar: '/src/assets/heroBg/hero_bg_2.jpg',
    creditURL: 'https://www.x.com',
  },
  {
    id: 2,
    src: heroBg2,
    alt: '室内植物',
    credit: '王小明',
    creditTitle: '植物爱好者',
    creditAvatar: '/src/assets/heroBg/hero_bg_2.jpg',
    creditURL: 'https://www.x.com',
  },
  {
    id: 3,
    src: heroBg1,
    alt: '观叶植物',
    credit: '张园园',
    creditTitle: '植物学研究员',
    creditAvatar: '/src/assets/heroBg/hero_bg_2.jpg',
    creditURL: 'https://www.x.com',
  },
  {
    id: 4,
    src: heroBg2,
    alt: '花卉植物',
    credit: '李花花',
    creditTitle: '花艺设计师',
    creditAvatar: '/src/assets/heroBg/hero_bg_2.jpg',
    creditURL: 'https://www.x.com',
  },
]

// 当前选中的封面图片索引
const currentImageIndex = ref(0)

// 进度条百分比
const progressPercentage = ref(0)

// 自动切换定时器
let autoChangeTimer: number | null = null
let progressTimer: number | null = null

// 切换封面图片
const changeCoverImage = (index: number) => {
  // 重置进度条
  progressPercentage.value = 0

  // 清除现有定时器
  if (autoChangeTimer) {
    clearTimeout(autoChangeTimer)
  }
  if (progressTimer) {
    clearInterval(progressTimer)
  }

  // 设置新索引
  currentImageIndex.value = index

  // 重新启动自动切换
  startAutoChange()
}

// 自动切换到下一个图片
const changeToNextImage = () => {
  const nextIndex = (currentImageIndex.value + 1) % coverImages.length
  changeCoverImage(nextIndex)
}

// 启动自动切换
const startAutoChange = () => {
  // 设置5秒后自动切换
  autoChangeTimer = window.setTimeout(() => {
    changeToNextImage()
  }, 6000)

  // 启动进度条动画
  startProgressAnimation()
}

// 启动进度条动画
const startProgressAnimation = () => {
  // 重置进度条
  progressPercentage.value = 0

  // 清除现有进度定时器
  if (progressTimer) {
    clearInterval(progressTimer)
  }

  // 设置进度条动画，每50ms更新一次，总共5000ms
  const interval = 50
  const steps = 5000 / interval
  const increment = 100 / steps

  progressTimer = window.setInterval(() => {
    if (progressPercentage.value < 100) {
      progressPercentage.value += increment
    } else {
      if (progressTimer) {
        clearInterval(progressTimer)
      }
    }
  }, interval)
}

// 当前显示的图片
const currentImage = ref(coverImages[currentImageIndex.value])

// 监听图片索引变化
const updateCurrentImage = () => {
  currentImage.value = coverImages[currentImageIndex.value]
}

// 监听索引变化
watch(currentImageIndex, () => {
  updateCurrentImage()
})

// 组件挂载时启动自动切换
onMounted(() => {
  startAutoChange()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (autoChangeTimer) {
    clearTimeout(autoChangeTimer)
  }
  if (progressTimer) {
    clearInterval(progressTimer)
  }
})
</script>

<template>
  <div class="relative w-full h-[80vh] overflow-hidden">
    <!-- 背景图层 - 使用绝对定位和透明度实现淡入淡出 -->
    <div
      v-for="(image, index) in coverImages"
      :key="image.id"
      class="absolute inset-0 bg-cover bg-center transition-opacity duration-700"
      :style="{
        backgroundImage: `url(${image.src})`,
        opacity: currentImageIndex === index ? 1 : 0,
      }"
    ></div>
    <!-- 内容区域 -->
    <div class="relative max-w-7xl mx-auto h-full px-2 md:px-6 lg:px-1">
      <div class="flex flex-col items-start justify-center h-full max-w-xl">
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight mb-6">
          中文社区热门植物最完整的自由百科全书
        </h1>
        <p class="text-lg md:text-xl text-white/80 mb-8">
          提供专业的植物可行性和栽培知识库，每一种植物都能自己的故事，每一片叶子都承载生长的奥秘
        </p>
        <button
          class="px-6 py-3 bg-primary-900 hover:bg-primary-1000 tracking-wider text-white font-medium rounded-md transition-colors cursor-pointer"
        >
          开始探索 <Icon icon="lucide:arrow-right" width="20" height="20" class="inline" />
        </button>
      </div>
    </div>

    <!-- 右侧图片选择器 -->
    <div class="absolute right-6 top-1/2 transform -translate-y-1/2 flex flex-col gap-4">
      <button
        v-for="(image, index) in coverImages"
        :key="image.id"
        @click="changeCoverImage(index)"
        class="w-6 h-6 rounded-full overflow-hidden border-[1.5px] border-white/50 opacity-50 transition-all cursor-pointer relative"
        :class="[
          currentImageIndex === index
            ? 'border-primary-900 opacity-100 scale-110'
            : 'border-white/50 scale-100',
        ]"
      >
        <img :src="image.src" :alt="image.alt" class="w-full h-full object-cover" />

        <!-- 进度条 -->
        <svg
          v-if="currentImageIndex === index"
          class="absolute inset-0 w-full h-full"
          viewBox="0 0 36 36"
        >
          <!-- 进度圆环 - 从0度开始，顺时针旋转 -->
          <circle
            cx="18"
            cy="18"
            r="15"
            fill="none"
            stroke-width="4"
            stroke="rgba(255, 255, 255, 0.8)"
            stroke-dasharray="94.2"
            :stroke-dashoffset="94.2 * (1 - progressPercentage / 100)"
            transform="rotate(-90 18 18)"
            class="transition-all duration-100 ease-linear"
          />
        </svg>
      </button>
    </div>

    <!-- 右下角图片来源信息 -->
    <div class="absolute right-6 bottom-6 text-white/50">
      <div class="m-1 text-white/80 text-[10px]">封面图片来源：</div>
      <div
        class="flex items-center bg-black/20 backdrop-blur-sm px-3 py-2 rounded-xl cursor-pointer"
      >
        <div class="w-6 h-6 border-1 border-white/50 rounded-full overflow-hidden mr-2">
          <img :src="currentImage.creditAvatar" alt="头像" class="w-full h-full object-cover" />
        </div>
        <div class="text-white">
          <p class="text-[10px] font-medium">{{ currentImage.credit }}</p>
          <p class="text-[10px] text-white/70">{{ currentImage.creditTitle }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 添加背景图片淡入淡出效果 */
.bg-cover {
  transition: opacity 700ms ease;
}
</style>
