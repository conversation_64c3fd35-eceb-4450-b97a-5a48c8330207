<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 数据统计项
interface StatItem {
  value: number
  label: string
  targetValue: number
}

// 统计数据
const stats = ref<StatItem[]>([
  { value: 321, label: '网红小众植物', targetValue: 321 },
  { value: 1130, label: '高清的植物海报', targetValue: 1130 },
  { value: 12, label: '内容共建者', targetValue: 12 },
])

// 数字增长动画
const animateNumbers = () => {
  const duration = 2000 // 动画持续时间（毫秒）
  const steps = 60 // 动画步数
  const interval = duration / steps

  stats.value.forEach((stat) => {
    const increment = stat.targetValue / steps
    let current = 0
    const timer = setInterval(() => {
      current += increment
      if (current >= stat.targetValue) {
        stat.value = stat.targetValue
        clearInterval(timer)
      } else {
        stat.value = Math.floor(current)
      }
    }, interval)
  })
}

// 组件挂载时启动动画
onMounted(() => {
  // 使用 Intersection Observer 检测元素是否可见
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          animateNumbers()
          observer.disconnect() // 动画只需执行一次
        }
      })
    },
    { threshold: 0.1 },
  )

  // 观察统计部分
  const statsSection = document.querySelector('.stats-section')
  if (statsSection) {
    observer.observe(statsSection)
  }
})
</script>

<template>
  <section class="py-16 px-4">
    <div class="max-w-7xl mx-auto">
      <!-- 标题和介绍部分 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
        <div class="md:col-span-1">
          <h2
            class="text-md font-bold text-gray-800 bg-gray-100 inline-block px-6 py-3 rounded-lg tracking-wider"
          >
            关于我们
          </h2>
        </div>
        <div class="md:col-span-3 space-y-6">
          <p class="text-gray-700 leading-relaxed">
            我们是一个纯公益性质的植物知识共享平台，致力于打造中文社区最全面、最权威的网红植物知识库与图鉴平台。拥有大多数的数据和内容来自社区植物爱好者贡献见识，他们不仅贡献了这里海量的植物养护习性数据、高清图鉴资源等，最终以免费、开放的姿态，为更多植物爱好者、园艺新手、科研学者等群体提供专业知识服务。
          </p>
          <p class="text-gray-700 leading-relaxed">
            我们秉持共建共享的理念，诚邀每一位热爱植物的伙伴加入，共同完善植物数据、分享养护经验。未来，我们将深度融合
            AI
            大模型与图形图像识别技术，实现植物智能识别、个性化养护方案定制等功能，让植物知识获取更便捷、养护指导更精准，以公益之心，筑植物知识殿堂，期待与您携手，让绿色智慧惠及更多人。
          </p>
        </div>
      </div>

      <!-- 统计数据部分 -->
      <div class="grid grid-cols-1 md:grid-cols-3 md:gap-20 gap-10">
        <div
          v-for="(stat, index) in stats"
          :key="index"
          class="bg-gray-50 rounded-3xl px-10 py-20 flex flex-col items-center justify-center text-center"
        >
          <div class="text-8xl text-primary-900 mb-16">{{ stat.value }}</div>
          <div class="text-gray-600">{{ stat.label }}</div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped></style>
