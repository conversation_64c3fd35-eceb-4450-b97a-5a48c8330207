<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

// 模拟植物分类数据
const categories = [
  { id: 1, name: '热门推荐', active: true },
  { id: 2, name: '秋海棠', active: false },
  { id: 3, name: '龟背竹', active: false },
  { id: 4, name: '龙舌兰', active: false },
  { id: 5, name: '块根类', active: false },
]

// 模拟植物数据
const plants = [
  {
    id: 1,
    name: { chinese: '绿鬣蜥海棠', latin: 'Begonia maculata' },
    image_url: '/src/assets/category/kuaigen.png',
    category_id: 2,
  },
  {
    id: 2,
    name: { chinese: '绿鬣蜥海棠', latin: 'Begonia maculata' },
    image_url: '/src/assets/category/kuaigen.png',
    category_id: 2,
  },
  {
    id: 3,
    name: { chinese: '绿鬣蜥海棠', latin: 'Begonia maculata' },
    image_url: '/src/assets/category/kuaigen.png',
    category_id: 3,
  },
  {
    id: 4,
    name: { chinese: '绿鬣蜥海棠', latin: 'Begonia maculata' },
    image_url: '/src/assets/category/kuaigen.png',
    category_id: 3,
  },
  {
    id: 5,
    name: { chinese: '绿鬣蜥海棠', latin: 'Begonia maculata' },
    image_url: '/src/assets/category/kuaigen.png',
    category_id: 4,
  },
  {
    id: 6,
    name: { chinese: '绿鬣蜥海棠', latin: 'Begonia maculata' },
    image_url: '/src/assets/category/kuaigen.png',
    category_id: 4,
  },
  {
    id: 7,
    name: { chinese: '绿鬣蜥海棠', latin: 'Begonia maculata' },
    image_url: '/src/assets/category/kuaigen.png',
    category_id: 5,
  },
  {
    id: 8,
    name: { chinese: '绿鬣蜥海棠', latin: 'Begonia maculata' },
    image_url: '/src/assets/category/kuaigen.png',
    category_id: 5,
  },
]

// 当前选中的分类
const selectedCategoryId = ref(1)

// 根据选中的分类筛选植物
const filteredPlants = computed(() => {
  if (selectedCategoryId.value === 1) {
    // 热门推荐显示所有植物
    return plants
  }
  return plants.filter((plant) => plant.category_id === selectedCategoryId.value)
})

// 切换分类
const selectCategory = (categoryId: number) => {
  selectedCategoryId.value = categoryId
  categories.forEach((category) => {
    category.active = category.id === categoryId
  })
}

// 路由跳转
const router = useRouter()
const viewAllPlants = () => {
  router.push('/plant/category')
}
</script>

<template>
  <section class="mb-30 px-4">
    <!-- 标题区域和描述 -->
    <div class="flex flex-col md:flex-row justify-between items-center mb-10">
      <div class="text-left">
        <h2 class="text-black/80 text-2xl md:text-3xl tracking-wider font-bold mb-2">
          浏览热门和趋势的植物
        </h2>
        <p class="text-gray-600 max-w-2xl mx-auto tracking-widest">
          发现那些受到植物爱好者喜爱的绿色伙伴
        </p>
      </div>

      <div class="max-w-md text-right">
        <p class="text-gray-400/50 text-xs">
          BIOPOKEMON 目前累计收录了 32 科 82 属 共 232 种热门网红植物， 并且以每周 20
          种的速度持续更新维护。如果你跟我一样热爱植物， 有足够的热情希望跟我一起参与网站内容的共建
          →
          <a href="#" class="text-black/80 hover:underline">请与我取得联系</a>
        </p>
      </div>
    </div>

    <!-- 分类标签 -->
    <div class="flex flex-wrap gap-2 md:gap-4 my-10">
      <button
        v-for="category in categories"
        :key="category.id"
        @click="selectCategory(category.id)"
        :class="[
          'px-4 py-2 rounded-md text-sm transition-all duration-300 cursor-pointer font-bold tracking-wider',
          category.active
            ? 'bg-primary-900 text-white'
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
        ]"
      >
        {{ category.name }}
      </button>
    </div>

    <!-- 植物卡片网格 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-20 gap-y-10 mb-10">
      <div
        v-for="plant in filteredPlants"
        :key="plant.id"
        class="group overflow-hidden transition-all duration-300 cursor-pointer"
      >
        <!-- 植物图片 -->
        <div class="aspect-3/4 overflow-hidden rounded-lg">
          <img
            :src="plant.image_url"
            :alt="plant.name.chinese"
            class="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-500"
          />
        </div>

        <!-- 植物信息 -->
        <div class="p-2">
          <h3 class="font-medium text-lg text-gray-900">{{ plant.name.chinese }}</h3>
          <p class="text-gray-500 text-sm italic">{{ plant.name.latin }}</p>
        </div>
      </div>
    </div>

    <!-- 查看全部按钮 -->
    <div class="text-center">
      <button
        @click="viewAllPlants"
        class="inline-flex items-center text-sm px-8 py-2 border-2 bg-white border-gray-100 text-black/80 rounded-xl hover:border-gray-200 transition-colors duration-300 cursor-pointer"
      >
        浏览全部
      </button>
    </div>
  </section>
</template>
