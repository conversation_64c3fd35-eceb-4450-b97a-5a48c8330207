<script setup lang="ts">
import { useRouter } from 'vue-router'

// 简化的FAQ数据结构
const faqs = [
  {
    content: '配土攻略和原则',
    url: '/learn/soil-guide',
    size: 'lg',
  },
  {
    content: '植物在不同生长阶段的施肥指南？',
    url: '/learn/fertilizer-guide',
    size: 'sm',
  },
  {
    content: '植物在不同生长阶段的施肥指南？植物在不同生长阶段的施肥指南？',
    url: '/learn/fertilizer-guide-detail',
    size: 'sm',
  },
  {
    content: '室内植物的正确摆放位置以及如何判断直射光、散射光等？？',
    url: '/learn/light-guide',
    size: 'lg',
  },
  {
    content: '植物在不同生长阶段的施肥指南？',
    url: '/learn/watering-guide',
    size: 'sm',
  },
  {
    content: '见干见湿和 干透浇透的精髓',
    url: '/learn/watering-principles',
    size: 'sm',
  },
  {
    content: '见干见湿和 干透浇透的精髓',
    url: '/learn/watering-principles',
    size: 'lg',
  },
]

// 路由跳转
const router = useRouter()
const viewAllFAQ = () => {
  router.push('/learn')
}

// 问题点击跳转
const navigateToFAQ = (url: string) => {
  router.push(url)
}
</script>

<template>
  <section class="mb-30 px-4">
    <!-- 使用flex布局分为左右两部分 -->
    <div class="flex flex-col md:flex-row gap-40 items-start justify-between">
      <!-- 左侧图片 -->
      <div class="md:w-1/3 relative overflow-hidden rounded-2xl">
        <img
          src="/src/assets/indexIllustration/index_faq_illustration.jpg"
          alt="植物养护问题"
          class="w-full h-full object-cover aspect-square"
        />
      </div>

      <!-- 右侧标题和FAQ云效果 -->
      <div class="md:w-2/3 flex flex-col gap-12">
        <!-- 标题区域 -->
        <div>
          <h2 class="text-black/80 text-2xl md:text-3xl tracking-wider font-bold mb-2">
            养护常见问题
          </h2>
          <p class="text-gray-600 max-w-2xl tracking-widest">发现那些意外的植物</p>
        </div>

        <!-- FAQ云效果 - 使用flex布局分成3行 -->
        <div class="flex flex-col gap-5">
          <!-- 第一行：2/5 + 3/5，左右模块都左对齐且垂直居中 -->
          <div class="flex flex-col md:flex-row gap-5">
            <div class="md:w-2/5 flex items-start">
              <div
                class="cursor-pointer font-medium text-4xl text-black/80 faq-item faq-item-lg text-left"
                @click="navigateToFAQ(faqs[0].url)"
              >
                {{ faqs[0].content }}
              </div>
            </div>
            <div class="md:w-3/5 flex flex-col gap-2 items-start">
              <div
                class="cursor-pointer text-sm text-gray-600 faq-item faq-item-sm text-left"
                @click="navigateToFAQ(faqs[1].url)"
              >
                {{ faqs[1].content }}
              </div>
              <div
                class="cursor-pointer text-sm text-gray-600 faq-item faq-item-sm text-left"
                @click="navigateToFAQ(faqs[2].url)"
              >
                {{ faqs[2].content }}
              </div>
            </div>
          </div>

          <!-- 第二行：占满整行，左对齐 -->
          <div class="flex w-full">
            <div
              class="cursor-pointer text-4xl font-medium text-black/80 faq-item faq-item-lg text-left"
              @click="navigateToFAQ(faqs[3].url)"
            >
              {{ faqs[3].content }}
            </div>
          </div>

          <!-- 第三行：2/5 + 3/5，左右模块都左对齐且垂直居中 -->
          <div class="flex flex-col md:flex-row gap-5">
            <div class="md:w-2/5 flex flex-col gap-2 items-start">
              <div
                class="cursor-pointer text-sm text-gray-600 faq-item faq-item-sm text-left"
                @click="navigateToFAQ(faqs[4].url)"
              >
                {{ faqs[4].content }}
              </div>
              <div
                class="cursor-pointer text-sm text-gray-600 faq-item faq-item-sm text-left"
                @click="navigateToFAQ(faqs[5].url)"
              >
                {{ faqs[5].content }}
              </div>
            </div>
            <div class="md:w-3/5 flex items-start">
              <div
                class="cursor-pointer text-4xl font-medium text-black/80 faq-item faq-item-lg text-left"
                @click="navigateToFAQ(faqs[6].url)"
              >
                {{ faqs[6].content }}
              </div>
            </div>
          </div>
        </div>

        <!-- 查看全部按钮 - 从居中改为居左对齐 -->
        <div class="text-left mt-8">
          <button
            @click="viewAllFAQ"
            class="inline-flex items-center text-sm px-8 py-2 border-2 bg-white border-gray-100 text-black/80 rounded-xl hover:border-gray-200 transition-colors duration-300 cursor-pointer"
          >
            浏览全部
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
/* FAQ项目基础样式 */
.faq-item {
  display: inline-block;
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
  text-decoration-color: rgba(34, 197, 94, 0);
  text-decoration-line: underline;
  text-decoration-skip-ink: none; /* 确保下划线不会在字母g、j等下方中断 */
  text-underline-offset: 1px; /* 初始偏移量减小，让下划线更靠近文字 */
  transition:
    text-decoration-color 0.3s ease,
    text-decoration-thickness 0.3s ease,
    text-underline-offset 0.3s ease;
}

/* 大文字（text-4xl）下划线样式 */
.faq-item-lg {
  text-decoration-thickness: 0;
}

.faq-item-lg:hover {
  text-decoration-color: rgba(10, 92, 92, 0.3);
  text-decoration-thickness: 10px;
  text-underline-offset: -3px; /* 悬停时下划线与文字底部轻微触碰 */
}

/* 小文字（text-sm）下划线样式 */
.faq-item-sm {
  text-decoration-thickness: 0;
}

.faq-item-sm:hover {
  text-decoration-color: rgba(10, 92, 92, 0.3);
  text-decoration-thickness: 5px;
  text-underline-offset: -2px; /* 悬停时下划线与文字底部轻微触碰 */
}
</style>
