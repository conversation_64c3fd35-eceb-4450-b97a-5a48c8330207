<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 导入现有图片资源作为占位图
import heroBg1 from '@/assets/heroBg/hero_bg_1.jpg'
import heroBg2 from '@/assets/heroBg/hero_bg_2.jpg'
import placeholderImg from '@/assets/placeholder.jpg'

// 特性数据
const features = [
  {
    id: 1,
    title: '植物习性速查，养护数据精准匹配',
    description: '输入植物名称快速习性速查，养护日历，光照水频率助力照顾速查，科学养护不走弯路',
    image: heroBg1,
  },
  {
    id: 2,
    title: '探秘绿植宇宙，收藏你的心动清单',
    description:
      '发现你喜欢的植物种类，添加到你的收藏，随时查看，排除杂念，一键访问打造专属植物档案',
    image: heroBg2,
  },
  {
    id: 3,
    title: 'AI 定制养护方案，环境适配无死角',
    description: '输入所在城市，房间环境，AI 给出适用你家环境的养护建议，零顾虑，生态植物全覆盖',
    image: placeholderImg,
  },
]

// 当前选中的特性索引
const currentIndex = ref(0)
// 进度条百分比
const progressPercent = ref(0)
// 定时器
let timer: number | null = null
// 进度条更新间隔（毫秒）
const updateInterval = 50
// 特性切换时间（毫秒）
const switchInterval = 10000
// 每次更新进度增加的百分比
const progressStep = (updateInterval / switchInterval) * 100

// 更新进度条
const updateProgress = () => {
  progressPercent.value += progressStep

  // 当进度达到100%时，切换到下一个特性
  if (progressPercent.value >= 100) {
    progressPercent.value = 0
    currentIndex.value = (currentIndex.value + 1) % features.length
  }
}

// 手动选择特性
const selectFeature = (index: number) => {
  currentIndex.value = index
  progressPercent.value = 0
}

// 启动定时器
const startTimer = () => {
  if (timer) clearInterval(timer)
  timer = window.setInterval(updateProgress, updateInterval)
}

// 组件挂载时启动定时器
onMounted(() => {
  startTimer()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) clearInterval(timer)
})
</script>

<template>
  <section class="mt-20 mb-30 px-4">
    <div class="text-center mb-16">
      <h2 class="text-black/80 text-2xl md:text-3xl tracking-wider font-bold mb-2">
        掌握植物养护习性从未如此简单
      </h2>
      <p class="text-gray-600 max-w-2xl mx-auto tracking-widest">
        获取您喜爱植物的全面护理技巧和窍门
      </p>
    </div>

    <div class="flex flex-col gap-20 md:flex-row items-center">
      <!-- 左侧特性列表 (1/3宽度) -->
      <div class="w-full md:w-2/5 pr-0 md:pr-8">
        <div class="space-y-2">
          <div
            v-for="(feature, index) in features"
            :key="feature.id"
            class="relative p-8 pt-5 transition-all duration-500 cursor-pointer rounded-md"
            :class="[index === currentIndex ? 'bg-gray-50' : 'bg-transparent hover:bg-gray-50/80']"
            @click="selectFeature(index)"
          >
            <!-- 左侧进度条 -->
            <div
              class="absolute left-0 top-0 bottom-0 w-[2px]"
              :class="[index === currentIndex ? 'bg-white/50' : '']"
            >
              <!-- 动态进度条 -->
              <div
                v-if="index === currentIndex"
                class="absolute left-0 top-0 w-full bg-gray-200 transition-all duration-200 ease-linear"
                :style="{ height: `${progressPercent}%` }"
              ></div>
            </div>

            <!-- 特性编号 -->
            <div
              class="inline-block font-medium mb-2 px-3 py-1 rounded-sm w-auto text-xs"
              :class="[
                index === currentIndex ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600',
              ]"
            >
              {{ index < 9 ? '0' + (index + 1) : index + 1 }}
            </div>

            <!-- 特性标题 -->
            <h3 class="text-md tracking-wider font-bold mb-2">{{ feature.title }}</h3>

            <!-- 特性描述 -->
            <p class="text-gray-400 text-sm leading-4">
              {{ feature.description }}
            </p>
          </div>
        </div>
      </div>

      <!-- 右侧图片区域 (2/3宽度) -->
      <div class="w-full md:w-2/3 mt-8 md:mt-0">
        <div class="relative h-[400px] rounded-lg overflow-hidden">
          <!-- 使用transition-group实现图片切换动画 -->
          <transition-group name="fade">
            <div
              v-for="(feature, index) in features"
              :key="feature.id"
              v-show="index === currentIndex"
              class="absolute inset-0 bg-cover bg-center"
              :style="{ backgroundImage: `url(${feature.image})` }"
            ></div>
          </transition-group>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
/* 图片淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.7s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
