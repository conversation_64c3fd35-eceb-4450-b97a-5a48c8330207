<script setup lang="ts">
import { Icon } from '@iconify/vue'

interface UserReview {
  id: number
  rating: number
  content: string
  userName: string
  userTitle: string
  userAvatar: string
}

// 模拟用户评论数据
const reviews: UserReview[] = [
  {
    id: 1,
    rating: 5,
    content:
      '作为收藏了87种小众植物的"绿拇指"，这个平台简直是我的"电子标本册"！从马达加斯加角异肉的稀有品种，到空气凤梨的湿度偏好，数据精确到让我惊喜。最贴心的AI推荐服务让我的养护自动调整养护建议，现在我种植物的状态都像遵身定制经完美，连同志爱花友都迫不及待想要尝试体验',
    userName: '林子琪',
    userTitle: '多肉收藏家',
    userAvatar: 'src/assets/heroBg/hero_bg_2.jpg',
  },
  {
    id: 2,
    rating: 5,
    content:
      '原本只是想养好家里的几盆多肉，没想到居平台的植物商业指南开启了副业！从多肉拼盘的搭配技巧，到盆栽造型的特性知识，这里既有专业的中深文章"指指点点"，也有群内经验植友直接经验60%，客户总说"你养的植物状态真好"——真实是平台的 AI 养护提醒比我厉害还准时！感谢这个让以爱变成职业的"植物学工厂"！',
    userName: '林子琪',
    userTitle: '多肉收藏家',
    userAvatar: 'src/assets/heroBg/hero_bg_2.jpg',
  },
  {
    id: 3,
    rating: 5,
    content:
      '"植样植博士 3 年来，终于找到能一站式搞定"内容的宝藏平台！高清图鉴里能找物品都"看得超清"，全是能够实时拍柄花花瓣风景的照片，都能快速匹配培基资源。收藏功能还能生成"深度讲解"，我在植物展会上拍照识别，从珍品到养护知识输出无缝衔接，粉丝都说我的植物全都被照顾得高级感扑面而来"',
    userName: '林子琪',
    userTitle: '多肉收藏家',
    userAvatar: 'src/assets/heroBg/hero_bg_2.jpg',
  },
]

// 生成星级评分数组
const getStars = (rating: number) => {
  return Array(rating).fill(null)
}
</script>

<template>
  <section class="mt-20 mb-30 px-4">
    <div class="text-center mb-16">
      <h2 class="text-black/80 text-2xl md:text-3xl tracking-wider font-bold mb-2">
        被无数植物收藏家所信任
      </h2>
      <p class="text-gray-600 max-w-2xl mx-auto tracking-widest">
        来自小红书、抖音等内容平台意见领袖收藏机推荐
      </p>

      <div class="relative mt-10">
        <!-- 评论卡片容器 -->
        <div class="flex flex-col md:flex-row justify-center items-stretch gap-20">
          <!-- 使用v-for循环渲染评论卡片 -->
          <div
            v-for="review in reviews"
            :key="review.id"
            class="group w-full bg-gray-50 hover:bg-primary rounded-[26px] p-1 transition-all duration-300 flex flex-col"
          >
            <div class="p-10 bg-white rounded-[23px] flex flex-col h-full">
              <!-- 星级评分 -->
              <div class="flex mb-4">
                <span v-for="(_, i) in getStars(review.rating)" :key="i" class="text-primary-900">
                  <Icon icon="material-symbols:star-rounded" width="16" height="16" />
                </span>
              </div>

              <!-- 评论内容 -->
              <p class="text-gray-700 mb-6 flex-grow text-base text-left tracking-wider leading-7">
                {{ review.content }}
              </p>
            </div>

            <!-- 用户信息 -->
            <div class="flex items-center my-5 px-8">
              <img
                :src="review.userAvatar"
                :alt="review.userName"
                class="w-12 h-12 rounded-full mr-4 object-cover"
              />
              <div class="flex flex-col items-start">
                <h4 class="font-semibold text-gray-900 group-hover:text-white">
                  {{ review.userName }}
                </h4>
                <p class="text-gray-500 text-sm group-hover:text-white/80">
                  {{ review.userTitle }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped></style>
