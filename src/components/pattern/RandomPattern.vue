<script setup lang="ts">
import { computed, defineProps, withDefaults } from 'vue'

// 定义 Props 接口
interface Props {
  color?: string
  opacity?: number
}

// 使用 withDefaults 设置默认值
const props = withDefaults(defineProps<Props>(), {
  color: '#0a5c5c',
  opacity: 0.5,
})

// 可用的 pattern 类名
const patternClasses = [
  'pattern-dots-lg',
  // 'pattern-horizontal-lines-md',
  'pattern-vertical-lines-sm',
  // 'pattern-grid-md',
  'pattern-diagonal-lines-md',
  // 'pattern-triangles-lg',
  // 'pattern-zigzag-xl',
  // 'pattern-horizontal-stripes-xl',
  // 'pattern-vertical-stripes-lg',
  // 'pattern-diagonal-stripes-lg',
]

// 随机选择一个 pattern 类
const randomPatternClass = computed(() => {
  const randomIndex = Math.floor(Math.random() * patternClasses.length)
  return patternClasses[randomIndex]
})

// 计算样式
const patternStyle = computed(() => {
  return {
    width: '300px',
    height: '600px',
    color: props.color,
    opacity: props.opacity,
    zIndex: '-1',
    position: 'relative',
  }
})
</script>

<template>
  <div :class="randomPatternClass" :style="patternStyle" class="inline-block"></div>
</template>

<style scoped>
@import '@/assets/pattern.min.css';
</style>
