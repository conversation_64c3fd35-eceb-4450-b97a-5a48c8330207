<script setup lang="ts">
import { ref, computed } from 'vue'
import HealthBar from '@/components/common/healthbar/HealthBar.vue'
import SoilMix from '@/components/common/soil/SoilMix.vue'
import TagList from '@/components/common/tag/TagList.vue'
import PHLevel from '@/components/common/phlevel/PHLevel.vue'

// 定义标签项接口
interface TagItem {
  value: string
  name?: string
}

// 定义范围接口
interface Range {
  min: number | null
  max: number | null
}

// 定义土壤混合接口
interface SoilMixType {
  [key: string]: number | string
}

// 定义组件 Props 接口
interface Props {
  tags?: TagItem[]
  soilMix: SoilMixType | Record<string, number> | string
  soil_ph?: Range // 新增：土壤酸碱度范围
  soil_notes?: string // 新增：土壤相关注意事项
  lightRange: Range
  lightExtreme: Range
  temperatureRange: Range
  temperatureExtreme: Range
  humidityRange: Range
  humidityExtreme: Range
}

// 使用 TypeScript 泛型定义 props
const props = withDefaults(defineProps<Props>(), {
  tags: () => [
    { value: '观叶植物' },
    { value: '喜湿不耐旱', name: '习性' },
    { value: '见干见湿', name: '浇水' },
    { value: '散射光', name: '光源' },
    { value: '养护简单' },
  ],
  soilMix: () => ({
    SPHAGNUM: 5,
    PINE_BARK: 30,
    PERLITE: 20,
    PEAT: 30,
    VERMICULITE: 5,
    LAVA_ROCK: 10,
  }),
  soil_ph: () => ({ min: 5.5, max: 6.5 }), // 默认土壤酸碱度范围
  soil_notes: undefined, // 默认无土壤注意事项
  lightRange: () => ({ min: 2000, max: 18000 }),
  lightExtreme: () => ({ min: 1200, max: 20000 }),
  temperatureRange: () => ({ min: 20, max: 30 }),
  temperatureExtreme: () => ({ min: -5, max: 35 }),
  humidityRange: () => ({ min: 40, max: 70 }),
  humidityExtreme: () => ({ min: 20, max: 90 }),
})

// 转换土壤组件格式
const soilComponents = computed(() => {
  if (!props.soilMix) return []

  // 处理对象情况（JSON 对象）
  if (typeof props.soilMix === 'object' && props.soilMix !== null) {
    // 过滤掉非数字值的属性
    const entries = Object.entries(props.soilMix).filter(
      ([_, value]) => !isNaN(Number(value)) && typeof value !== 'object',
    )

    return entries.map(([name, percentage]) => ({
      name,
      percentage: Number(percentage),
    }))
  }

  // 处理字符串情况（兼容性保留）
  if (typeof props.soilMix === 'string' && props.soilMix.trim()) {
    try {
      const parsed = JSON.parse(props.soilMix)
      if (typeof parsed === 'object' && parsed !== null) {
        return Object.entries(parsed).map(([name, percentage]) => ({
          name,
          percentage: Number(percentage),
        }))
      }
    } catch (e) {
      console.error('解析土壤混合数据失败:', e)
    }
  }

  return []
})

// 定义当前选中的标签页
const activeTab = ref('养护基础')
const tabs = ['养护基础', '进阶知识']

// 切换标签页
const switchTab = (tab: string) => {
  activeTab.value = tab
}
</script>

<template>
  <div class="w-full">
    <!-- 标签页切换 -->
    <div class="flex border-b-[0.5px] border-gray-200 mb-8">
      <div
        v-for="tab in tabs"
        :key="tab"
        class="font-medium px-3 py-3 cursor-pointer relative text-gray-400"
        :class="{ '!text-black': activeTab === tab }"
        @click="switchTab(tab)"
      >
        {{ tab }}
        <!-- 选中的下划线 -->
        <div
          v-if="activeTab === tab"
          class="absolute bottom-0 left-0 w-full h-[2px] bg-primary-900"
        ></div>
      </div>
    </div>

    <!-- 标签页内容区域 -->
    <div class="mb-8 space-y-12">
      <!-- tag 部分 -->
      <div>
        <div class="text-xs font-medium text-gray-900 mb-2">关键词:</div>
        <TagList :tags="props.tags" />
      </div>

      <!-- 配土部分 -->
      <div>
        <div class="text-xs font-medium text-gray-900 mb-2">配土:</div>
        <!-- <PHLevel :min="props.soil_ph.min" :max="props.soil_ph.max" /> -->
        <SoilMix :soils="soilComponents" :soil_ph="props.soil_ph" :soil_notes="props.soil_notes" />
      </div>

      <!-- 健康条部分 -->
      <div class="space-y-1">
        <HealthBar name="light" :range="props.lightRange" :extreme="props.lightExtreme" />

        <HealthBar
          name="temperature"
          :range="props.temperatureRange"
          :extreme="props.temperatureExtreme"
        />
        <HealthBar name="humidity" :range="props.humidityRange" :extreme="props.humidityExtreme" />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
