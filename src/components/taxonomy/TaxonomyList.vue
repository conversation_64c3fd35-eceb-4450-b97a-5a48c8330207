<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useTaxonomyStore } from '@/stores/taxonomy'
import { useRouter } from 'vue-router'

const router = useRouter()

// 定义分类数据的接口
interface TaxonomySpecies {
  id: number
  name: string
  latinName: string
}

interface TaxonomyGenus {
  name: string
  species: TaxonomySpecies[]
}

interface TaxonomyFamily {
  family: string
  genera: TaxonomyGenus[]
}

interface Props {
  // 文字大小，默认为 text-sm
  textSize?: 'text-sm' | 'text-md' | 'text-lg'
  // 可以传入自定义的分类数据，如果不传则使用默认数据
  customTaxonomyData?: TaxonomyFamily[]
}

const props = defineProps<Props>()

// 定义事件
const emit = defineEmits(['species-click', 'close'])

// 使用 taxonomy store
const taxonomyStore = useTaxonomyStore()

// 计算属性：获取分类数据
const taxonomyPlants = computed(() => props.customTaxonomyData || taxonomyStore.taxonomyData)

// 加载状态
const isLoading = computed(() => taxonomyStore.loading)

// 计算文字大小样式
const textSizeClass = computed(() => props.textSize || 'text-sm')

// 在组件挂载时获取分类数据
onMounted(async () => {
  if (!props.customTaxonomyData) {
    await taxonomyStore.fetchTaxonomyData()
  }
})

// 处理种点击事件
const handleSpeciesClick = (species: TaxonomySpecies) => {
  // 将拉丁名转换为URL格式
  const latinNameUrl = species.latinName
    ? species.latinName.replace(/\s/g, '_')
    : `species-${species.id}`
  router.push(`/plant/${latinNameUrl}`)

  // 触发关闭事件
  emit('close-popup')
}

// 处理科点击事件
const handleFamilyClick = (family: string) => {
  router.push(`/plant/taxonomy`)
  emit('close-popup')
}

// 处理属点击事件
const handleGenusClick = (genus: string) => {
  router.push(`/plant/taxonomy`)
  emit('close-popup')
}
</script>

<template>
  <div class="w-full">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex justify-center items-center py-10">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
    </div>

    <!-- 科属种分类列表 -->
    <div v-else>
      <!-- 科名称显示 -->
      <div v-for="(familyItem, familyIndex) in taxonomyPlants" :key="familyIndex" class="mb-4 flex">
        <div
          class="w-1/6 mr-10 flex items-center justify-end font-medium text-gray-400 self-start cursor-pointer hover:text-primary-900 transition-colors"
          :class="textSizeClass"
          @click="handleFamilyClick(familyItem.family)"
        >
          <span>{{ familyItem.family }}</span>
          <Icon icon="ic:round-arrow-right" width="14" height="14" class="ml-1" />
        </div>

        <div class="flex-1">
          <!-- 属循环 -->
          <div v-for="(genusItem, genusIndex) in familyItem.genera" :key="genusIndex" class="mb-6">
            <!-- 属标题 -->
            <div
              class="text-gray-500 mb-1 font-medium mb-2 cursor-pointer hover:text-primary-900 transition-colors"
              :class="textSizeClass"
              @click="handleGenusClick(genusItem.name)"
            >
              {{ genusItem.name }}
            </div>

            <!-- 种列表 -->
            <div class="flex flex-wrap gap-1 items-center">
              <template
                v-for="(speciesItem, speciesIndex) in genusItem.species"
                :key="speciesIndex"
              >
                <div
                  class="hover:text-primary-900 hover:font-medium inline-flex items-center cursor-pointer transition-colors"
                  :class="textSizeClass"
                  @click="handleSpeciesClick(speciesItem)"
                >
                  {{ speciesItem.name }}
                </div>
                <div
                  v-if="speciesIndex < genusItem.species.length - 1"
                  class="text-gray-200 px-1 text-xs inline-flex"
                >
                  <Icon icon="iconamoon:sign-division-slash-bold" width="14" height="14" />
                </div>
              </template>
            </div>
          </div>

          <!-- 科之间的分隔 -->
          <div
            v-if="familyIndex < taxonomyPlants.length - 1"
            class="my-4 h-[0.5px] bg-gray-100"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
