<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

// ===================== 响应式状态区域 =====================
// 用于存储各元素的引用
const sidebarRef = ref<HTMLElement | null>(null)
const mainRef = ref<HTMLElement | null>(null)
const containerRef = ref<HTMLElement | null>(null)

// 存储各种测量值
const headerHeight = ref(0)
const windowHeight = ref(0)

// 计算侧边栏的样式
const sidebarStyle = computed(() => {
  return {
    position: 'sticky',
    top: `${headerHeight.value}px`,
    height: `calc(100vh - ${headerHeight.value}px)`,
    overflowY: 'auto' as const,
  }
})

// 计算主内容区域的最小高度
const mainMinHeight = computed(() => {
  return `calc(100vh - ${headerHeight.value}px)`
})

// ===================== 生命周期钩子和事件处理 =====================
// 更新各种测量值
const updateMeasurements = () => {
  // 获取 header 高度
  const header = document.querySelector('header')
  if (header) {
    headerHeight.value = header.getBoundingClientRect().height
  }

  // 获取窗口高度
  windowHeight.value = window.innerHeight
}

// 窗口大小变化事件处理函数
const handleResize = () => {
  updateMeasurements()
}

// 组件挂载时
onMounted(() => {
  // 初始化测量值
  updateMeasurements()

  // 添加事件监听器
  window.addEventListener('resize', handleResize)

  // 确保在 DOM 完全渲染后再次更新测量值
  setTimeout(updateMeasurements, 100)
})

// 组件卸载时
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div ref="containerRef" class="max-w-7xl mx-auto w-full">
    <div class="flex flex-col md:flex-row gap-10">
      <!-- 侧边栏区域 -->
      <div ref="sidebarRef" class="sidebar-container w-full md:w-1/4" :style="sidebarStyle">
        <slot name="sidebar"></slot>
      </div>

      <!-- 主内容区域 -->
      <div
        ref="mainRef"
        class="main-container w-full md:w-3/4"
        :style="{ minHeight: mainMinHeight }"
      >
        <slot name="main"></slot>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 确保滚动条样式一致 */
.sidebar-container::-webkit-scrollbar {
  width: 2px;
}

.sidebar-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sidebar-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 2px;
}

.sidebar-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
