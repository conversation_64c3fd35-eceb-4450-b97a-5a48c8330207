<script setup lang="ts">
import { ref } from 'vue'
import placeholderImage from '@/assets/placeholder.jpg'
import { Icon } from '@iconify/vue'
import TaxonomyList from '@/components/taxonomy/TaxonomyList.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 定义事件
const emit = defineEmits(['close', 'hover', 'species-click'])

// 菜单分类
const subMenus = [
  {
    id: 1,
    name: '热门划分',
    icon: 'category',
    description: '热门物种总划分',
  },
  {
    id: 2,
    name: '科属种划分',
    icon: 'science',
    description: '科属种的详细划分',
  },
  {
    id: 3,
    name: '随机',
    icon: 'celebrate',
    description: '随机展示一种植物',
  },
]

// 获取分类图标
const getCategoryIcon = (iconName) => {
  switch (iconName) {
    case 'category':
      return '/src/assets/icons/category.svg'
    case 'science':
      return '/src/assets/icons/science.svg'
    case 'celebrate':
      return '/src/assets/icons/celebrate.svg'
    default:
      return ''
  }
}

// 热门划分的植物列表
const popularPlantCategories = [
  { id: 1, name: '秋海棠', count: 23, image: '/src/assets/placeholder.jpg' },
  { id: 2, name: '龟背竹', count: 43, image: placeholderImage },
  { id: 3, name: '龙角树', count: 21, image: placeholderImage },
  { id: 4, name: '多肉植物', count: 37, image: placeholderImage },
  { id: 5, name: '夏威夷', count: 11, image: placeholderImage },
  { id: 6, name: '仙人掌', count: 43, image: placeholderImage },
  { id: 7, name: '花烛', count: 21, image: placeholderImage },
]

// 当前选中的子菜单
const activeSubmenu = ref(1)

// 处理左侧子菜单点击
const handleSubmenuClick = (submenuId) => {
  activeSubmenu.value = submenuId
}

const handleCategoryClick = (category: string) => {
  router.push(`/plant/category`)
  emit('close')
}

// 处理鼠标悬停
const handleMouseEnter = () => {
  emit('hover', true)
}

const handleMouseLeave = () => {
  emit('hover', false)
  emit('close')
}
</script>

<template>
  <div
    class="max-h-[760px] absolute left-0 right-0 top-full mt-4 m-4 overflow-hidden bg-white rounded-xl shadow-md z-50 border-[0.5px] border-b-1 border-gray-200"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="flex">
      <!-- 左侧分类列表 -->
      <div class="pt-8 pb-32 w-1/3 bg-gray-50">
        <h3 class="pl-10 text-sm font-medium text-gray-800 mb-4">发现喜欢的植物</h3>

        <div class="space-y-4">
          <div
            v-for="subMenu in subMenus"
            :key="subMenu.id"
            class="text-sm group py-4 pl-9 flex items-center cursor-pointer border-l transition-colors"
            :class="{
              'bg-white  text-primary-900 border-l-primary ': activeSubmenu === subMenu.id,
              'border-l-transparent hover:bg-white': activeSubmenu !== subMenu.id,
            }"
            @click="handleSubmenuClick(subMenu.id)"
            @mouseenter="handleSubmenuClick(subMenu.id)"
          >
            <!-- 图标 -->
            <div
              class="p-2 flex items-center justify-center rounded-lg bg-white hover:bg-primary-100 text-primary-700 mr-4 shadow-md group-hover:shadow-none group-hover:bg-primary-100 transition-shadow duration-200"
              :class="{ 'shadow-none !bg-primary-50': activeSubmenu === subMenu.id }"
            >
              <img :src="getCategoryIcon(subMenu.icon)" width="32" height="32" alt="" />
            </div>
            <div>
              <div class="font-semibold">{{ subMenu.name }}</div>
              <div class="text-xs text-gray-400">{{ subMenu.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="w-2/3 p-8">
        <!-- 热门划分 -->
        <div v-if="activeSubmenu === 1" class="grid grid-cols-3 gap-4">
          <div
            v-for="plantCategory in popularPlantCategories"
            :key="plantCategory.id"
            class="h-20 rounded-lg overflow-hidden relative group cursor-pointer hover:shadow-md"
            @click="handleCategoryClick(plantCategory.name)"
          >
            <img
              :src="plantCategory.image"
              :alt="plantCategory.name"
              class="w-full h-full object-cover group-hover:scale-120 transition-transform duration-400"
            />
            <div
              class="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent pt-8 pb-2 px-2"
            >
              <div class="font-semibold text-sm text-white">
                {{ plantCategory.name }} ({{ plantCategory.count }})
              </div>
            </div>
          </div>
        </div>

        <!-- 科属种划分 -->
        <div v-if="activeSubmenu === 2" class="relative">
          <div class="overflow-y-auto max-h-[400px] scrollbar-hide">
            <TaxonomyList
              textSize="text-sm"
              @species-click="(species) => $emit('species-click', species)"
              @close-popup="emit('close')"
            />
            <!-- @close-popup 事件是 TaxonomyList 组件中 handleSpeciesClick 触发的； -->
          </div>

          <!-- 右下角固定链接 -->
          <div class="absolute -bottom-6 -right-2 text-xs text-gray-400 bg-white py-1 px-2">
            <router-link
              :to="{ name: 'taxonomy' }"
              class="flex items-center"
              @click="emit('close')"
            >
              在单独界面浏览
              <Icon icon="si:arrow-right-duotone" width="18" height="18" />
              <span class="text-black px-1 underline hover:bg-gray-200 hover:font-bold">查看</span>
            </router-link>
          </div>
        </div>

        <!-- 随机 -->
        <div v-if="activeSubmenu === 3" class="flex items-center justify-center h-64">
          <div class="w-2/3 p-8">
            <div class="text-xl font-medium mb-2">鹦鱼秋海棠</div>
            <div class="text-sm text-gray-500 mb-4">秋海棠科 秋海棠属</div>
            <button class="px-4 py-2 bg-primary-700 text-white rounded-md flex items-center">
              了解详情
              <svg
                class="w-4 h-4 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                ></path>
              </svg>
            </button>
            <div class="mt-4 text-xs text-gray-400">换一个 ↺</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 确保弹出菜单的过渡效果平滑 */
.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%;
}

.aspect-h-9 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
