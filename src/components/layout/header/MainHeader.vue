<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { RouterLink, useRoute } from 'vue-router'
import PopupMenu from './PopupMenu.vue'
import { Icon } from '@iconify/vue'

// 导航菜单项
const navItems = [
  { name: '首页', path: '/' },
  { name: '发现', path: '#', hasPopup: true }, // 修改 path 为 '#'，避免导航
  { name: '学习', path: '/learning' },
  { name: '收藏家', path: '/collections' },
  { name: '手作好物', path: '/handmade' },
]

// 控制弹出菜单的显示状态
const showPopup = ref(false)

// 控制移动端菜单显示状态
const mobileMenuOpen = ref(false)

// 处理导航项悬停
const handleNavHover = (item) => {
  showPopup.value = item.hasPopup || false
}

// 处理鼠标离开导航区域
const handleNavLeave = () => {
  setTimeout(() => {
    if (!isHoveringPopup.value) {
      showPopup.value = false
    }
  }, 200)
}

// 跟踪鼠标是否悬停在弹出菜单上
const isHoveringPopup = ref(false)
const handlePopupHover = (hovering) => {
  isHoveringPopup.value = hovering
}

// 处理弹出菜单关闭
const handlePopupClose = () => {
  showPopup.value = false
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

// 处理导航项点击
const handleNavClick = (item) => {
  if (item.hasPopup) {
    showPopup.value = true
  }
}

// 获取当前路由
const route = useRoute()

// 添加滚动监听相关逻辑
const scrollY = ref(0)
const isScrolled = computed(() => scrollY.value > 50)
const isHomePage = computed(() => route.path === '/')

// 监听滚动事件
const handleScroll = () => {
  scrollY.value = window.scrollY
}

// 计算header样式
const headerClass = computed(() => {
  // 如果不是首页，始终显示白色背景
  if (!isHomePage.value) {
    return 'bg-white border-gray-200'
  }

  // 如果是首页且已滚动，显示半透明白色背景
  if (isScrolled.value) {
    return 'bg-white/95 backdrop-blur-sm border-gray-200/80 '
  }

  // 首页且未滚动，显示透明背景
  return 'bg-transparent border-transparent'
})

// 计算文字颜色
const textClass = computed(() => {
  // 如果不是首页或已滚动，显示黑色文字
  if (!isHomePage.value || isScrolled.value) {
    return 'text-black'
  }

  // 首页且未滚动，显示白色文字
  return 'text-white'
})

// 组件挂载时添加滚动监听
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // 初始化滚动位置
  handleScroll()
})

// 组件卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<template>
  <header
    class="py-3 fixed top-0 left-0 right-0 w-full border-b-[0.5px] z-50 select-none transition-all duration-300"
    :class="headerClass"
  >
    <div class="max-w-7xl mx-auto px-4 flex items-center">
      <!-- Logo 区域 -->
      <div class="w-1/4 flex items-center justify-start">
        <RouterLink to="/" class="flex items-center">
          <img src="@/assets/logo.svg" alt="BioPokemon Logo" class="w-10 h-10 mr-4" />
          <span class="font-semibold transition-colors duration-300" :class="textClass"
            >BioPokemon</span
          >
        </RouterLink>
      </div>

      <!-- 导航菜单 - 桌面版 -->
      <nav class="w-2/4 hidden md:flex items-center justify-center" @mouseleave="handleNavLeave">
        <template v-for="(item, index) in navItems" :key="index">
          <!-- 普通菜单项使用 RouterLink -->
          <RouterLink
            v-if="!item.hasPopup"
            :to="item.path"
            class="btn-effect tracking-[1.4px] font-medium mx-2 py-1 md:px-2 cursor-pointer relative transition-colors duration-300"
            :class="[
              isHomePage && !isScrolled
                ? 'text-white/90 hover:text-black'
                : 'text-black/80 hover:text-black',
            ]"
            active-class="font-bold"
            @mouseenter="handleNavHover(item)"
          >
            {{ item.name }}
          </RouterLink>

          <!-- 有弹出菜单的项使用 div -->
          <div
            v-else
            class="btn-effect tracking-[1.4px] font-medium mx-2 py-1 md:px-2 cursor-pointer relative transition-colors duration-300"
            :class="[
              isHomePage && !isScrolled
                ? 'text-white/90 hover:text-black'
                : 'text-black/80 hover:text-black',
            ]"
            @mouseenter="handleNavHover(item)"
            @click="handleNavClick(item)"
          >
            {{ item.name }}
            <Icon
              icon="mingcute:down-fill"
              :class="[isHomePage && !isScrolled ? 'text-white/70' : 'hover:text-black']"
              width="11"
              height="11"
            />
          </div>
        </template>
      </nav>

      <!-- 搜索区域 -->
      <div class="w-1/4 flex items-center justify-end ml-auto md:ml-0">
        <span
          class="font-semibold mr-4 cursor-pointer p-1 btn-effect transition-colors duration-300"
          :class="[
            isHomePage && !isScrolled
              ? 'text-white/90 hover:text-white'
              : 'text-black/80 hover:text-black',
          ]"
        >
          <Icon icon="iconamoon:search-bold" width="20" height="20" />
        </span>

        <!-- 移动端菜单按钮 -->
        <button
          class="md:hidden flex items-center justify-center p-4 ml-4"
          @click="toggleMobileMenu"
        >
          <Icon
            icon="mingcute:menu-fill"
            width="20"
            height="20"
            class="cursor-pointer transition-colors duration-300"
            :class="[isHomePage && !isScrolled ? 'text-white/90' : 'text-black/80']"
          />
        </button>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 -translate-y-1"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 -translate-y-1"
    >
      <div v-if="mobileMenuOpen" class="md:hidden bg-white border-t border-gray-200">
        <div class="container mx-auto px-4 py-2">
          <template v-for="(item, index) in navItems" :key="index">
            <!-- 普通菜单项使用 RouterLink -->
            <RouterLink
              v-if="!item.hasPopup"
              :to="item.path"
              class="block py-1 px-2 mx-2 cursor-pointer text-black/80 hover:bg-gray-100"
              active-class="text-primary-900 font-medium"
              @click="mobileMenuOpen = false"
            >
              {{ item.name }}
            </RouterLink>

            <!-- 有弹出菜单的项使用 div -->
            <div
              v-else
              class="block py-1 px-2 mx-2 cursor-pointer text-black/80 hover:bg-gray-100"
              @click="(handleNavClick(item), (mobileMenuOpen = false))"
            >
              {{ item.name }}
            </div>
          </template>
        </div>
      </div>
    </transition>

    <!-- 弹出菜单 -->
    <transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 translate-y-1"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 translate-y-1"
    >
      <PopupMenu
        class="container mx-auto rounded-xl max-w-[800px] shadow-2xs"
        v-if="showPopup"
        @close="handlePopupClose"
        @hover="handlePopupHover"
      />
    </transition>
  </header>
</template>

<style scoped></style>
