<script setup lang="ts">
import { Icon } from '@iconify/vue'
import footerBg from '@/assets/gradientBg.jpg'

// 定义页脚导航链接
const footerLinks = {
  section1: {
    title: '知识分享',
    links: [
      { name: '秋海棠', url: '#' },
      { name: '龙舌兰', url: '#' },
      { name: '龟背竹', url: '#' },
      { name: '仙人掌', url: '#' },
      { name: '快速识别', url: '#' },
    ],
  },
  section2: {
    title: '花店',
    links: [
      { name: '花盆栽', url: '#' },
      { name: '黄绿植', url: '#' },
      { name: '收藏家', url: '#' },
      { name: '手作好物', url: '#' },
    ],
  },
  section3: {
    title: '友情链接',
    links: [
      { name: '小红书', url: '#' },
      { name: '中国自然科学网', url: '#' },
      { name: '植物图鉴', url: '#' },
      { name: '植物图鉴欣赏', url: '#' },
    ],
  },
}

// 联系信息
const contactInfo = {
  phone: '+1 (999) 999-99-99',
  email: '<EMAIL>',
}

// 地址信息
const addressInfo = {
  address: '1901 Thornridge Cir. Shiloh, Hawaii 81063',
  ageRestriction: '18+',
  score: 'Score for adults',
}

// 社交媒体链接
const socialLinks = [
  { name: 'telegram', icon: 'ic:baseline-telegram', url: '#' },
  { name: 'whatsapp', icon: 'ic:baseline-whatsapp', url: '#' },
]

// 版权信息
const copyrightYear = new Date().getFullYear()
</script>

<template>
  <footer
    class="py-12 relative bg-cover bg-center bg-no-repeat border-t-[0.5px] border-gray-100"
    :style="{ backgroundImage: `url(${footerBg})` }"
  >
    <div class="max-w-7xl mx-auto px-4 mt-5">
      <!-- footer 的卡片区域 -->
      <div class="bg-white rounded-2xl overflow-hidden pb-8 pt-12 px-10">
        <!-- 页脚顶部区域：Logo、导航和联系信息 -->
        <div class="flex flex-wrap justify-between mb-16">
          <!-- Logo 区域 -->
          <div class="w-full md:w-1/4 mb-8 md:mb-0">
            <div class="mb-6">
              <img src="@/assets/logo.svg" alt="BioPokemon" class="w-12 h-12" />
            </div>
          </div>

          <!-- 导航链接区域 -->
          <div class="w-full md:w-2/4 flex flex-wrap mb-8 md:mb-0">
            <!-- 第一列链接 -->
            <div class="w-1/3 mb-6 md:mb-0">
              <h3 class="text-xs font-medium text-gray-400 mb-4 select-none">
                {{ footerLinks.section1.title }}
              </h3>
              <ul class="space-y-1">
                <li v-for="link in footerLinks.section1.links" :key="link.name">
                  <a
                    :href="link.url"
                    class="text-sm text-gray-500 hover:text-primary-900 hover:font-medium"
                  >
                    {{ link.name }}
                  </a>
                </li>
              </ul>
            </div>

            <!-- 第二列链接 -->
            <div class="w-1/3">
              <h3 class="text-xs font-medium text-gray-400 mb-4 select-none">
                {{ footerLinks.section2.title }}
              </h3>
              <ul class="space-y-1">
                <li v-for="link in footerLinks.section2.links" :key="link.name">
                  <a
                    :href="link.url"
                    class="text-sm text-gray-500 hover:text-primary-900 hover:font-medium"
                  >
                    {{ link.name }}
                  </a>
                </li>
              </ul>
            </div>

            <!-- 第三列链接 -->
            <div class="w-1/3">
              <h3 class="text-xs font-medium text-gray-400 mb-4 select-none">
                {{ footerLinks.section3.title }}
              </h3>
              <ul class="space-y-1">
                <li v-for="link in footerLinks.section3.links" :key="link.name">
                  <a
                    :href="link.url"
                    class="text-sm text-gray-500 hover:text-primary-900 hover:font-medium"
                  >
                    {{ link.name }}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <!-- 联系信息区域 -->
          <div class="w-full md:w-1/4">
            <div class="text-right">
              <a
                href="#"
                class="inline-block px-4 py-2 bg-black text-white rounded-md text-sm font-medium mb-4"
              >
                Request a call
              </a>
              <div class="mb-1">
                <a href="{`tel:${contactInfo.phone}`}" class="text-sm text-gray-800 font-medium">
                  {{ contactInfo.phone }}
                </a>
              </div>
              <div>
                <a href="{`mailto:${contactInfo.email}`}" class="text-sm text-gray-500">
                  {{ contactInfo.email }}
                </a>
              </div>
            </div>
          </div>
        </div>
        <!-- 页脚底部区域：地址、社交媒体和版权 -->
        <div class="flex flex-wrap items-center justify-between pt-8">
          <!-- 社交媒体图标 -->
          <div class="flex space-x-4 mb-4 md:mb-0">
            <a
              v-for="link in socialLinks"
              :key="link.name"
              :href="link.url"
              class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-primary-100 hover:text-primary-700 transition-colors"
            >
              <Icon :icon="link.icon" width="20" height="20" />
            </a>
          </div>

          <!-- 地址信息 -->
          <div class="text-sm text-gray-500 mb-4 md:mb-0">
            {{ addressInfo.address }}
          </div>

          <!-- 年龄限制和评分 -->
          <div class="flex items-center">
            <div class="text-xl font-bold mr-4">{{ addressInfo.ageRestriction }}</div>
            <div class="text-xs text-gray-400">
              {{ addressInfo.score }}
            </div>
          </div>
        </div>
      </div>
      <!-- footer 的申明区域 -->
      <div class="mt-16">
        <!-- 申明信息 -->
        <div class="flex items-center justify-between mt-8 gap-20">
          <div class="w-1/2 text-4xl leading-[1.5] text-white font-extrabold">
            如果你在 BioPokemon找不到你喜欢的植物，或者其他任何问题，都可以与我取得联系
          </div>
          <div class="w-1/2 text-xs text-white">占位</div>
        </div>
        <!-- 版权和隐私信息 -->
        <div class="flex items-center justify-between mt-8">
          <div class="text-xs text-white">© {{ copyrightYear }} • Copyright</div>
          <div class="text-xs text-white">隐私政策</div>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
/* 可以根据需要添加自定义样式 */
</style>
