<script setup lang="ts">
import { computed, defineProps, ref, withDefaults, useSlots, onMounted, onUnmounted } from 'vue'
import { Icon } from '@iconify/vue'

// 定义 Props 接口
interface Props {
  clamp?: number
  expandMode?: 'none' | 'expand' | 'fullscreen'
}

// 使用 withDefaults 设置默认值
const props = withDefaults(defineProps<Props>(), {
  clamp: 3,
  expandMode: 'none',
})

// 获取插槽
const slots = useSlots()

// 控制文本展开状态
const isExpanded = ref(false)
// 控制全屏显示状态
const isFullscreen = ref(false)

// 计算是否需要显示"更多"提示
const showMoreText = computed(() => {
  // 只要文本未展开且展开模式不为none时就显示提示，不需要鼠标悬停
  return !isExpanded.value && props.expandMode !== 'none'
})

// 检查文本是否被截断
const hasTextOverflow = ref(false)
const textRef = ref<HTMLElement | null>(null)

// 检测文本是否溢出
const checkTextOverflow = () => {
  if (textRef.value) {
    const element = textRef.value
    hasTextOverflow.value = element.scrollHeight > element.clientHeight
  }
}

// 在组件挂载后检查文本溢出
const checkOverflowOnMount = () => {
  // 在下一个渲染周期检查文本溢出
  setTimeout(checkTextOverflow, 0)
}

// 处理文本展开/收起
const toggleExpand = () => {
  if (props.expandMode === 'none') return

  if (props.expandMode === 'expand') {
    isExpanded.value = !isExpanded.value
  } else if (props.expandMode === 'fullscreen') {
    isFullscreen.value = !isFullscreen.value
  }
}

// 关闭全屏显示
const closeFullscreen = () => {
  isFullscreen.value = false
}

// 处理键盘 ESC 键事件
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isFullscreen.value) {
    closeFullscreen()
  }
}

// 组件挂载和卸载时添加/移除键盘事件监听
onMounted(() => {
  checkOverflowOnMount()
  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  // 移除键盘事件监听
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<template>
  <div>
    <!-- 正常显示的文本 -->
    <div class="relative" :class="$attrs.class">
      <div class="relative">
        <p
          v-if="!isExpanded"
          ref="textRef"
          class="text-ellipsis overflow-hidden relative"
          :class="{
            'line-clamp-1': props.clamp === 1,
            'line-clamp-2': props.clamp === 2,
            'line-clamp-3': props.clamp === 3,
            'line-clamp-4': props.clamp === 4,
            'line-clamp-5': props.clamp === 5,
            'line-clamp-6': props.clamp === 6,
            'line-clamp-7': props.clamp === 7,
            'line-clamp-8': props.clamp === 8,
            'line-clamp-9': props.clamp === 9,
            'line-clamp-10': props.clamp === 10,
          }"
        >
          <slot></slot>
          <!-- 自定义"更多"提示 -->
          <span
            v-if="showMoreText && hasTextOverflow"
            class="absolute right-0 bottom-0 inline-flex items-center justify-center cursor-pointer more-text"
            @click.stop="toggleExpand"
          >
            <span class="text-gray-600 hover:text-black">(更多)</span>
          </span>
        </p>
        <p v-else class="transition-all duration-300">
          <slot></slot>
        </p>
      </div>
    </div>

    <!-- 全屏显示模式 -->
    <div
      v-if="isFullscreen"
      class="fixed inset-0 bg-black/95 z-50 flex items-center justify-center p-8 overflow-auto transition-opacity duration-900 ease-in-out"
      @click.self="closeFullscreen"
    >
      <!-- 固定在屏幕右上角的关闭按钮 -->
      <div class="fixed top-4 right-4 z-10">
        <div class="items-center gap-2">
          <Icon
            icon="ep:close"
            class="text-white cursor-pointer hover:text-gray-300 transition-colors"
            width="28"
            height="28"
            @click="closeFullscreen"
          />
          <span class="text-white text-xs opacity-30">ESC</span>
        </div>
      </div>

      <!-- 内容容器 -->
      <div class="relative max-w-3xl w-full max-h-[80vh] overflow-y-auto duration-900 ease-in-out">
        <div class="text-white p-8">
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义行数限制类 */
.line-clamp-1,
.line-clamp-2,
.line-clamp-3,
.line-clamp-4,
.line-clamp-5,
.line-clamp-6,
.line-clamp-7,
.line-clamp-8,
.line-clamp-9,
.line-clamp-10 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: clip;
}

.line-clamp-1 {
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  -webkit-line-clamp: 4;
}

.line-clamp-5 {
  -webkit-line-clamp: 5;
}

.line-clamp-6 {
  -webkit-line-clamp: 6;
}

.line-clamp-7 {
  -webkit-line-clamp: 7;
}

.line-clamp-8 {
  -webkit-line-clamp: 8;
}

.line-clamp-9 {
  -webkit-line-clamp: 9;
}

.line-clamp-10 {
  -webkit-line-clamp: 10;
}

/* 自定义"更多"提示样式 */
.more-text {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 30%);
  padding-left: 30px;
  padding-right: 10px;
}
</style>
