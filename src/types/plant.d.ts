// 从 api.ts 导入 ApiResponse 接口
import type { ApiResponse } from './api'

// 枚举类型
export enum PropagationMethod {
  SEED = '种子',
  CUTTING = '扦插',
  DIVISION = '分株',
  GRAFTING = '嫁接',
  SPORE = '孢子',
  OFFSET = '分生',
  LAYERING = '压条',
  LEAF_CUTTING = '叶插',
}

export enum SoilComponent {
  PEAT = '泥炭土',
  PERLITE = '珍珠岩',
  COCO_COIR = '椰壳',
  SPHAGNUM = '水苔',
  AKADAMA = '赤玉土',
  VERMICULITE = '蛭石',
  LAVA_ROCK = '火山岩',
  PUMICE = '浮石',
  PINE_BARK = '松树皮',
}

export enum RarityLevel {
  VERY_COMMON = 1,
  COMMON = 2,
  UNCOMMON = 3,
  RARE = 4,
  VERY_RARE = 5,
}

export enum DifficultyLevel {
  VERY_EASY = 1,
  EASY = 2,
  MODERATE = 3,
  DIFFICULT = 4,
  VERY_DIFFICULT = 5,
}

export enum WateringStyle {
  SEE_DRY_SEE_WET = '见干见湿',
  DRY_THOROUGHLY_WATER_THOROUGHLY = '干透浇透',
}

// 基础模型
export interface Taxonomy {
  chinese?: string
  english?: string
  latin?: string
}

export interface Range {
  min?: number
  max?: number
}

export interface SoilMix {
  components: Record<string, number>
  description?: string
}

// 植物科
export interface PlantFamily {
  id: number
  name: Taxonomy
  description?: string
}

// 植物属
export interface PlantGenus {
  id: number
  family_id: number
  name: Taxonomy
  description?: string
}

// 植物图片
export interface PlantImage {
  id: number
  plant_id: number
  image_url: string
  caption?: string
  is_primary: boolean
  display_order: number
  created_at?: string
  updated_at?: string
}

// 植物种
export interface PlantSpecies {
  id: number
  genus_id: number
  name: Taxonomy
  alias?: string
  description?: string
  rarity?: RarityLevel
  difficulty?: DifficultyLevel
  image?: string

  // 环境需求
  temperature_range?: Range
  temperature_extreme?: Range
  light_range?: Range
  light_extreme?: Range
  humidity_range?: Range
  humidity_extreme?: Range

  // 原产地和栖息地
  origin?: string[]
  habitat?: string

  // 浇水习性
  watering_notes?: string
  watering_style?: WateringStyle

  // 繁殖方式
  sexual_propagation?: boolean
  asexual_propagation?: string[]

  // 配土
  soil_mix?: SoilMix

  created_at?: string
  updated_at?: string
}

// 包含图片的植物种详情
export interface PlantSpeciesWithImages extends PlantSpecies {
  images: PlantImage[]
}

// 植物分类
export interface PlantCategory {
  id: number
  name: string
  description?: string
}

export type { ApiResponse }
