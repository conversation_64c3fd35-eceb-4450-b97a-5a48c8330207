import axios from 'axios'

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL, // API 的 base_url
  timeout: Number(import.meta.env.VITE_API_TIMEOUT), // 请求超时时间
  headers: {
    'Content-Type': 'application/json', // 默认发送 JSON 格式数据
  },
})

// request 拦截器
service.interceptors.request.use(
  (config) => {
    // 这里可以添加请求前的处理逻辑，例如添加 token
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers['Authorization'] = `Bearer ${token}`;
    // }
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  },
)

// response 拦截器
service.interceptors.response.use(
  (response) => {
    // 直接返回响应数据
    return response.data
  },
  (error) => {
    console.error('响应拦截器错误:', error)
    // 这里可以根据错误状态码做不同的处理
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 未授权处理
          console.error('未授权，请重新登录')
          break
        case 403:
          // 禁止访问处理
          console.error('拒绝访问')
          break
        case 404:
          // 资源不存在处理
          console.error('请求的资源不存在')
          break
        case 500:
          // 服务器错误处理
          console.error('服务器错误')
          break
        default:
          console.error(`未知错误: ${error.response.status}`)
      }
    } else {
      console.error('网络错误或请求被取消')
    }
    return Promise.reject(error)
  },
)

export default service
