## 使用Axios完成API网络请求

### 安装 axios

### 1. 创建 .env 文件 并配置环境变量

前端项目也经常需要根据不同的环境（开发、生产）配置不同的变量，例如后端 API 的地址。Vite 对.env 文件提供了原生支持。但需要注意，前端. env 绝对不能存放任何敏感信息，只能存放那些在前端代码（浏览器端）中被允许访问的变量。比如：

在 Vue 组件或任何 `.js`/`.ts` 文件中，可以通过 `import.meta.env` 访问这些变量。

```tsx
// src/api/user.ts

import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL
console.log(import.meta.env.VITE_APP_TITLE)
```

### 2. 配置 axios 实例和基础 URL

在一个集中的地方来统一配置 Axios，包括设置基础 URL 和可能的拦截器。建议在 `src/utils/` 或者 `src/api/` 路径下创建 axios 统一实例对象。

```bash
# my-vue3-project

cd src
mkdir utils && cd utils

touch request.ts
```

```tsx
import axios from 'axios';

// 创建 axios 实例
const service = axios.create({
  baseURL: '/api/v1', // API 的 base_url
  timeout: 5000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json', // 默认发送 JSON 格式数据
  },
});

// request 拦截器
service.interceptors.request.use(
  ...
);

// response 拦截器
service.interceptors.response.use(
  ...
);

export default service;
```

### 3. 定义数据类型（Interfaces/Types）

类似于 Python 中 Pydantic 的数据校验作用，声明了一种自定义的特殊数据模型。通过定义这些接口，当在调用 API 函数和处理返回数据时，TypeScript 会进行类型检查，提醒你使用了错误的数据结构或属性名，这能极大地提高开发效率和代码健壮性。

建议在 `src/types/` 目录下创建，需要尽可能根据 FastAPI 项目中定义的 Pydantic 模型来精确定义。Swagger UI 文档的 "Schemas" 部分会提供这些模型的具体字段和类型。

### 4. 实现具体的 API 服务

根据后端接口的分类（例如：认证、用户等），在 `src/api/` 目录下创建对应的模块文件。每个文件负责该分类下的所有 API 调用。

————————

## 使用Pinia Store 集中管理数据

负责管理应用的状态（数据），协调 API 调用，并将获取的数据存储起来的工作。在最佳实践中：通常**不会**在组件 `src/components/...vue` 或者 视图`src/views/...vue` 中直接调用 API 函数，而是统一通过 Pinia store 的 actions 来调用 API，并将获取的数据存储在 store 的 state 中。组件始终是从 store 中获取数据并渲染到网页上。

### 1. 修改和创建不同的 Pina Store

以资源为对象，调用接口函数，把数据存储在 store 的 state 中，使其具备**数据响应式**。包含两部分关键的操作：

- 状态、数据
- 将 API 接口中的函数在 store 中包了一层

核心是将对数据的写操作也能够保证数据响应式，

```tsx
// src/stores/user.ts

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getUsers, getUser, createUser } from '@/api/users' // 导入用户 API 函数
import { User, UserCreatePayload } from '@/types/user' // 导入用户类型
import { login } from '@/api/auth' // 导入认证 API 函数
import { LoginPayload, LoginResponse } from '@/types/auth' // 导入认证类型

export const useUserStore = defineStore('user', () => {
  // State - 使用 ref 定义响应式状态
  const users = ref<User[]>([]) // 存储用户列表
  const currentUser = ref<User | null>(null) // 存储当前登录用户或查看的用户
  const loading = ref(false)
  const error = ref<string | null>(null)
  // 存储认证 token
  const accessToken = ref<string | null>(localStorage.getItem('access_token') || null)

  // Getters - 使用 computed 定义计算属性
  const isLoggedIn = computed(() => accessToken.value !== null)

  // Actions - 定义为普通函数

  // 调用登录 API 并处理结果
  const loginUser = async (credentials: LoginPayload) => {
    loading.value = true
    error.value = null
    try {
      const response: LoginResponse = await login(credentials)
      accessToken.value = response.access_token
      // 将 token 存储到 localStorage，以便刷新页面后仍能保持登录状态
      localStorage.setItem('access_token', response.access_token)
      // TODO: 登录成功后，可能需要获取当前用户信息并存储到 currentUser
      // await fetchCurrentUser(); // 假设有一个 fetchCurrentUser action
    } catch (error: any) {
      error.value = error.message || 'Login failed'
      accessToken.value = null
      localStorage.removeItem('access_token')
      // 错误已经在 axiosInstance 的响应拦截器中处理了一部分，这里可以做进一步处理或仅记录错误
      console.error('Login Action Error:', error)
      // 抛出错误以便组件可以捕获并显示提示
      throw error
    } finally {
      loading.value = false
    }
  }

  // 调用获取单个用户 API
  const fetchUser = async (userId: number) => {
    loading.value = true
    error.value = null
    try {
      const user: User = await getUser(userId) // 调用 api/users.ts 中的 getUser 函数
      currentUser.value = user // 将获取的数据存储到 state
    } catch (error: any) {
      error.value = error.message || `Failed to fetch user ${userId}`
      console.error('Fetch User Action Error:', error)
      currentUser.value = null // 获取失败则清空当前用户
      throw error
    } finally {
      loading.value = false
    }
  }

  // 调用获取用户列表 API
  const fetchUsers = async () => {
    loading.value = true
    error.value = null
    try {
      const usersList: User[] = await getUsers() // 调用 api/users.ts 中的 getUsers 函数
      users.value = usersList // 将获取的数据存储到 state
    } catch (error: any) {
      error.value = error.message || 'Failed to fetch users'
      console.error('Fetch Users Action Error:', error)
      users.value = [] // 获取失败则清空用户列表
      throw error
    } finally {
      loading.value = false
    }
  }

  // 调用创建用户 API
  const createUserAction = async (userData: UserCreatePayload) => {
    loading.value = true
    error.value = null
    try {
      const newUser: User = await createUser(userData) // 调用 api/users.ts 中的 createUser 函数
      // 将新创建的用户添加到用户列表中，保持数据响应式
      users.value.push(newUser)
      return newUser
    } catch (error: any) {
      error.value = error.message || 'Failed to create user'
      console.error('Create User Action Error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出功能
  const logout = () => {
    accessToken.value = null
    currentUser.value = null
    localStorage.removeItem('access_token')
    // 可选：清空用户列表数据
    users.value = []
  }

  // 清除错误信息
  const clearError = () => {
    error.value = null
  }

  // 根据 ID 查找用户（从当前加载的用户列表中）
  const findUserById = (userId: number): User | undefined => {
    return users.value.find((user) => user.id === userId)
  }

  // TODO: 添加 updateUserAction 和 deleteUserAction 等

  // 返回所有需要暴露的状态、计算属性和方法
  return {
    // State
    users,
    currentUser,
    loading,
    error,
    accessToken,

    // Getters
    isLoggedIn,

    // Actions
    loginUser,
    fetchUser,
    fetchUsers,
    createUserAction,
    logout,
    clearError,
    findUserById,
  }
})
```

### 2. 在组件和视图层中使用 Pinia Store 中的数据和 Actions

将组件或者视图与到 Pinia store 进行连接，使用 storeToRefs 解构 state；对数据的读写操作都通过 store 进行的，不直接对 API 调用，这样的好处和目的是保持响应式。

```tsx
<script setup lang="ts">
import { onMounted } from 'vue';
import { useUserStore } from '@/stores/user';
import { storeToRefs } from 'pinia'; // 用于解构 store 的 state，保持响应式

const userStore = useUserStore();
// 使用 storeToRefs 解构 state，使其在模板中保持响应式
const { users, loading, error } = storeToRefs(userStore);

// 在组件挂载后获取用户列表数据
onMounted(() => {
  userStore.fetchUsers(); // 调用 store 中的 action
});

// 示例：处理创建用户的函数（可以在一个表单提交事件中调用）
const handleCreateUser = async (userData: any) => { // 实际应该传入 UserCreatePayload 类型
   try {
       const newUser = await userStore.createUserAction(userData);
       alert(`User ${newUser.email} created!`);
       // 创建成功后可以刷新列表或跳转
       // userStore.fetchUsers(); // 刷新列表
   } catch (err) {
       // 错误处理已在 store 的 action 中进行了一部分，这里可以显示更友好的用户提示
       alert(`Error creating user: ${userStore.error}`);
   }
};

</script>

<template>
  <div>
    <h1>用户列表</h1>

    <div v-if="loading">加载中...</div>
    <div v-else-if="error">{{ error }}</div>
    <ul v-else>
      <li v-for="user in users" :key="user.id">
        {{ user.email }} (ID: {{ user.id }}) - {{ user.is_active ? '活跃' : '非活跃' }}
      </li>
    </ul>

    <button @click="handleCreateUser({ email: '<EMAIL>', password: 'password123' })">创建示例用户</button>

  </div>
</template>

<style scoped>
/* 组件样式 */
</style>
```

- 组件通过 `useUserStore()` 获取 store 实例。
- 使用 `storeToRefs` 安全地从 store 的 state 中解构出响应式属性，这样在模板中使用它们时会随着 state 的变化而更新。
- 在 `onMounted` 生命周期钩子中调用 `userStore.fetchUsers()` action，触发 API 调用。
- 模板根据 `loading` 和 `error` 状态显示不同的内容。
- 通过 Pinia store，组件不需要关心数据是如何获取的细节（是 Axios 还是其他方式），也不需要直接管理数据的加载和错误状态，这都由 store 负责。
