from tortoise import fields
from .base import BaseModel, TimestampMixin
from .enums import (
    PropagationMethod, SoilComponent, RarityLevel, DifficultyLevel, WateringStyle,
    LightPreference, DroughtTolerance, HumidityPreference, ClimateZone
)

from typing import List, Optional, Dict, Any, Union
import json
# from tortoise.contrib.pydantic import pydantic_model_creator



class Taxonomy(fields.JSONField):
    """用于存储分类学信息的自定义字段，包含中文名、英文名和拉丁名"""
    
    def to_db_value(self, value: Dict[str, str], instance) -> str:
        if isinstance(value, dict):
            return json.dumps(value)
        return value
        
    def to_python_value(self, value: Union[str, Dict]) -> Dict:
        if isinstance(value, str):
            return json.loads(value)
        return value or {"chinese": "", "english": "", "latin": ""}


class PlantFamily(BaseModel):
    """植物科"""
    name = Taxonomy(description="科名（中文、英文、拉丁文）")
    description = fields.TextField(null=True)
    
    # 反向关系
    genera: fields.ReverseRelation["PlantGenus"]
    
    def __str__(self):
        names = self.name
        return f"{names.get('chinese', '')} ({names.get('latin', '')})"
    
    class Meta:
        table = "plant_families"


class PlantGenus(BaseModel):
    """植物属"""
    family = fields.ForeignKeyField('models.PlantFamily', related_name='genera')
    name = Taxonomy(description="属名（中文、英文、拉丁文）")
    description = fields.TextField(null=True)
    
    # 反向关系
    species: fields.ReverseRelation["PlantSpecies"]
    
    def __str__(self):
        names = self.name
        return f"{names.get('chinese', '')} ({names.get('latin', '')})"
    
    class Meta:
        table = "plant_genera"


class RangeField(fields.JSONField):
    """自定义字段，用于存储范围值"""
    
    def to_db_value(self, value: Dict[str, Any], instance) -> str:
        if isinstance(value, dict):
            return json.dumps(value)
        return value
        
    def to_python_value(self, value: Union[str, Dict]) -> Dict:
        if isinstance(value, str):
            return json.loads(value)
        return value or {"min": None, "max": None}


class SoilMix(fields.JSONField):
    """用于存储配土组合的自定义字段"""
    def to_db_value(self, value: Dict[str, Any], instance) -> str:
        if isinstance(value, dict):
            return json.dumps(value)
        return value
    
    def to_python_value(self, value: Union[str, Dict]) -> Dict:
        if isinstance(value, str):
            return json.loads(value)
        return value or {}
    

class PlantSpecies(BaseModel, TimestampMixin):
    """植物种"""
    # 基本信息
    genus = fields.ForeignKeyField('models.PlantGenus', related_name='species')
    name = Taxonomy(description="种名（中文、英文、拉丁文）")
    alias = fields.CharField(max_length=255, null=True, description="别名，以逗号分隔")
    description = fields.TextField(null=True, description="植物详细描述，支持Markdown格式")
    rarity = fields.IntEnumField(RarityLevel, null=True)
    difficulty = fields.IntEnumField(DifficultyLevel, null=True)
    image = fields.CharField(max_length=255, null=True)
    
    # 环境需求 - 温度
    temperature_range = RangeField(null=True, description="适宜温度范围，单位：摄氏度")
    temperature_extreme = RangeField(null=True, description="可接受极限温度范围，单位：摄氏度")
    temperature_notes = fields.TextField(null=True, description="温度相关注意事项")
    
    # 环境需求 - 光照
    light_range = RangeField(null=True, description="适宜光照范围，单位：Lux")
    light_extreme = RangeField(null=True, description="可接受极限光照范围，单位：Lux")
    light_preference = fields.CharEnumField(LightPreference, null=True, description="光照偏好")
    
    # 环境需求 - 湿度
    humidity_range = RangeField(null=True, description="适宜湿度范围，单位：百分比")
    humidity_extreme = RangeField(null=True, description="可接受极限湿度范围，单位：百分比")
    humidity_preference = fields.CharEnumField(HumidityPreference, null=True, description="空气湿度偏好")
    
    # 原产地
    origin = fields.JSONField(null=True, description="原产地列表")
    native_climate_zone = fields.CharEnumField(ClimateZone, null=True, description="原生气候")
    
    # 浇水习性
    watering_style = fields.CharEnumField(WateringStyle, null=True, description="浇水方式：见干见湿或干透浇透")
    watering_notes = fields.TextField(null=True, description="浇水注意事项")
    base_water_frequency = fields.IntField(null=True, description="基础浇水频率(天)-最佳温度/湿度/光照环境下")
    drought_tolerance = fields.CharEnumField(DroughtTolerance, null=True, description="土壤耐旱性")
    
    # 繁殖方式
    propagation_method = fields.JSONField(null=True, description="繁殖方式列表")
    
    # 配土
    soil_mix = SoilMix(null=True, description="配土组合及其百分比")
    soil_ph = RangeField(null=True, description="适宜土壤酸碱度范围")
    soil_notes = fields.TextField(null=True, description="土壤相关注意事项")
    
    # 反向关系
    images: fields.ReverseRelation["PlantImage"]
    
    def __str__(self):
        names = self.name
        return f"{names.get('chinese', '')} ({names.get('latin', '')})"
    
    class Meta:
        table = "plant_species"
        
        

class PlantCategory(BaseModel, TimestampMixin):
    """植物运营分类"""
    name = fields.CharField(max_length=100, description="运营分类名称")
    description = fields.TextField(null=True, description="运营分类描述")
    
    # 定义多对多关系
    species = fields.ManyToManyField(
        'models.PlantSpecies', related_name='categories', through='plant_species_categories'
    )
    
    def __str__(self):
        return self.name
    
    class Meta:
        table = "plant_categories"


class PlantImage(BaseModel, TimestampMixin):
    """植物图片"""
    plant = fields.ForeignKeyField('models.PlantSpecies', related_name='images')
    image_url = fields.CharField(max_length=255)
    caption = fields.CharField(max_length=255, null=True)
    is_primary = fields.BooleanField(default=False)
    display_order = fields.IntField(default=0)
    
    class Meta:
        table = "plant_images"
        unique_together = [("plant_id", "is_primary")]


# 创建用于API的Pydantic模型
# PlantFamilyPydantic = pydantic_model_creator(PlantFamily, name="PlantFamily")
# PlantGenusPydantic = pydantic_model_creator(PlantGenus, name="PlantGenus")
# PlantSpeciesPydantic = pydantic_model_creator(PlantSpecies, name="PlantSpecies")
# PlantCategoryPydantic = pydantic_model_creator(PlantCategory, name="PlantCategory")
