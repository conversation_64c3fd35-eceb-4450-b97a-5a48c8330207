from enum import Enum, StrEnum


class EnumBase(Enum):
    @classmethod
    def get_member_values(cls):
        return [item.value for item in cls._member_map_.values()]

    @classmethod
    def get_member_names(cls):
        return [name for name in cls._member_names_]


class MethodType(StrEnum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"


# 植物相关枚举类型
class PropagationMethod(str, EnumBase):
    """植物繁殖方式"""
    SEED = "配子结合"
    SPORE = "孢子"
    CUTTING = "扦插"
    DIVISION = "分株"
    GRAFTING = "嫁接"
    OFFSET = "分生"
    LAYERING = "压条"
    LEAF_CUTTING = "叶插"


class SoilComponent(str, EnumBase):
    """土壤组成成分"""
    PEAT = "泥炭土"
    PERLITE = "珍珠岩"
    COCO_COIR = "椰糠"
    SPHAGNUM = "水苔"
    AKADAMA = "赤玉土"
    VERMICULITE = "蛭石"
    LAVA_ROCK = "火山岩"
    PUMICE = "浮石"
    PINE_BARK = "松树皮"
    KANUMA = "鹿沼土"
    DIATOMITEL = "硅藻土"
    CHARCOAL = "园艺竹炭"
    PLANTS_ASH = "草木灰"
    HUMUS = "腐殖土"
    HINOHARA = "日向石"
    MISOSHI = "麦饭石"
    TAKASHI = "桐生砂"
    CERAMSITE = "陶粒"
    ZEOLITE = "沸石"


class RarityLevel(int, EnumBase):
    """市场稀缺性"""
    VERY_COMMON = 1
    COMMON = 2
    UNCOMMON = 3
    RARE = 4
    VERY_RARE = 5


class DifficultyLevel(int, EnumBase):
    """养护难度"""
    VERY_EASY = 1
    EASY = 2
    MODERATE = 3
    DIFFICULT = 4
    VERY_DIFFICULT = 5


class WateringStyle(str, EnumBase):
    """基础浇水分类"""
    SEE_DRY_SEE_WET = "见干见湿"
    DRY_THOROUGHLY_WATER_THOROUGHLY = "干透浇透"


class LightPreference(str, EnumBase):
    """光照偏好"""
    DIRECT_SUN = "直射光"
    BRIGHT_INDIRECT = "明亮散射光"
    MEDIUM_INDIRECT = "中等散射光"
    LOW_LIGHT = "低光"


class DroughtTolerance(str, EnumBase):
    """土壤耐旱性"""
    LOW = "低"
    MEDIUM = "中"
    HIGH = "高"


class HumidityPreference(str, EnumBase):
    """空气湿度偏好"""
    LOW = "低"
    MEDIUM = "中"
    HIGH = "高"


class ClimateZone(str, EnumBase):
    """气候区域"""
    A_TROPICAL = "热带气候"
    B_ARID = "干旱气候"
    C_TEMPERATE = "温带气候"
    D_CONTINENTAL = "寒带气候"
    E_POLAR = "极地气候"
