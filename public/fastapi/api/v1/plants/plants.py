import logging
from typing import List, Optional

from fastapi import APIRouter, Query, Body
from tortoise.expressions import Q

from app.controllers.plant import (
    plant_family_controller, 
    plant_genus_controller, 
    plant_species_controller,
    plant_category_controller,
    plant_image_controller
)
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.plant import (
    PlantFamilyCreate, PlantFamilyUpdate,
    PlantGenusCreate, PlantGenusUpdate,
    PlantSpeciesCreate, PlantSpeciesUpdate,
    PlantCategoryCreate, PlantCategoryUpdate,
    PlantImageCreate, PlantImageUpdate
)

logger = logging.getLogger(__name__)

router = APIRouter()

# 植物科 API
@router.get("/family/list", summary="查看植物科列表")
async def list_plant_family(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    name: str = Query("", description="科名，用于搜索（支持中文、英文或拉丁文）"),
):
    q = Q()
    total, families = await plant_family_controller.list(page=page, page_size=page_size, search=q)
    
    # 过滤名称
    if name:
        filtered_families = []
        for family in families:
            family_name = family.name
            if (name in family_name.get('chinese', '') or 
                name in family_name.get('english', '') or 
                name in family_name.get('latin', '')):
                filtered_families.append(family)
        families = filtered_families
        total = len(filtered_families)
    
    data = [await obj.to_dict() for obj in families]
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/family/get", summary="查看植物科详情")
async def get_plant_family(
    family_id: int = Query(..., description="科ID"),
):
    family = await plant_family_controller.get(id=family_id)
    family_dict = await family.to_dict()
    return Success(data=family_dict)


@router.post("/family/create", summary="创建植物科")
async def create_plant_family(
    family_in: PlantFamilyCreate,
):
    if await plant_family_controller.check_name_exists(family_in.name):
        return Fail(msg="名称已存在，中文名、英文名或拉丁名与已有记录重复")

    family = await plant_family_controller.create(obj_in=family_in)
    family_dict = await family.to_dict()
    return Success(data=family_dict, msg="创建成功")


@router.post("/family/update", summary="更新植物科")
async def update_plant_family(
    family_in: PlantFamilyUpdate,
):
    if await plant_family_controller.check_name_exists(family_in.name, exclude_id=family_in.id):
        return Fail(msg="名称已存在，中文名、英文名或拉丁名与已有记录重复")
        
    family = await plant_family_controller.update(id=family_in.id, obj_in=family_in)
    family_dict = await family.to_dict()
    return Success(data=family_dict, msg="更新成功")


@router.delete("/family/delete", summary="删除植物科")
async def delete_plant_family(
    family_id: int = Query(..., description="科ID"),
):
    # 检查是否有关联的属
    genera_count = await plant_genus_controller.model.filter(family_id=family_id).count()
    if genera_count > 0:
        return Fail(msg="无法删除：该科下存在属")
    
    await plant_family_controller.remove(id=family_id)
    return Success(msg="删除成功")


# 植物属 API
@router.get("/genus/list", summary="查看植物属列表")
async def list_plant_genus(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    name: str = Query("", description="属名，用于搜索（支持中文、英文或拉丁文）"),
    family_id: Optional[int] = Query(None, description="科ID，用于筛选"),
):
    q = Q()
    if family_id is not None:
        q &= Q(family_id=family_id)
    
    total, genera = await plant_genus_controller.list(page=page, page_size=page_size, search=q)
    
    # 过滤名称
    if name:
        filtered_genera = []
        for genus in genera:
            genus_name = genus.name
            if (name in genus_name.get('chinese', '') or 
                name in genus_name.get('english', '') or 
                name in genus_name.get('latin', '')):
                filtered_genera.append(genus)
        genera = filtered_genera
        total = len(filtered_genera)
    
    data = []
    for obj in genera:
        genus_dict = await obj.to_dict()
        # 添加科信息
        family = await plant_family_controller.get(id=obj.family_id)
        genus_dict["family"] = await family.to_dict()
        data.append(genus_dict)
    
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/genus/get", summary="查看植物属详情")
async def get_plant_genus(
    genus_id: int = Query(..., description="属ID"),
):
    genus = await plant_genus_controller.get(id=genus_id)
    genus_dict = await genus.to_dict()
    
    # 添加科信息
    family = await plant_family_controller.get(id=genus.family_id)
    genus_dict["family"] = await family.to_dict()
    
    return Success(data=genus_dict)


@router.post("/genus/create", summary="创建植物属")
async def create_plant_genus(
    genus_in: PlantGenusCreate,
):
    # 检查科是否存在
    try:
        await plant_family_controller.get(id=genus_in.family_id)
    except:
        return Fail(msg="指定的科不存在")

    if await plant_genus_controller.check_name_exists(genus_in.name):
        return Fail(msg="名称已存在，中文名、英文名或拉丁名与已有记录重复")

    genus = await plant_genus_controller.create(obj_in=genus_in)
    genus_dict = await genus.to_dict()
    return Success(data=genus_dict, msg="创建成功")
    


@router.post("/genus/update", summary="更新植物属")
async def update_plant_genus(
    genus_in: PlantGenusUpdate,
):
    # 检查科是否存在
    if genus_in.family_id is not None:
        try:
            await plant_family_controller.get(id=genus_in.family_id)
        except:
            return Fail(msg="指定的科不存在")
    
    if await plant_genus_controller.check_name_exists(genus_in.name, exclude_id=genus_in.id):
        return Fail(msg="名称已存在，中文名、英文名或拉丁名与已有记录重复")

    genus = await plant_genus_controller.update(id=genus_in.id, obj_in=genus_in)
    genus_dict = await genus.to_dict()
    return Success(data=genus_dict, msg="更新成功")


@router.delete("/genus/delete", summary="删除植物属")
async def delete_plant_genus(
    genus_id: int = Query(..., description="属ID"),
):
    # 检查是否有关联的种
    species_count = await plant_species_controller.model.filter(genus_id=genus_id).count()
    if species_count > 0:
        return Fail(msg="无法删除：该属下存在种")
    
    await plant_genus_controller.remove(id=genus_id)
    return Success(msg="删除成功")


# 植物种 API
@router.get("/species/list", summary="查看植物种列表")
async def list_plant_species(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    name: str = Query("", description="种名，用于搜索（支持中文、英文或拉丁文）"),
    genus_id: Optional[int] = Query(None, description="属ID，用于筛选"),
    rarity: Optional[int] = Query(None, description="稀有度，用于筛选"),
    difficulty: Optional[int] = Query(None, description="难度，用于筛选"),
    category_id: Optional[int] = Query(None, description="分类ID，用于筛选"),
):
    # 如果指定了分类ID，则需要特殊处理
    if category_id is not None:
        # 获取该分类下的所有植物
        category = await plant_category_controller.get(id=category_id)
        species_ids = [species.id for species in await category.species.all()]
        
        # 构建查询条件
        q = Q(id__in=species_ids)
        if genus_id is not None:
            q &= Q(genus_id=genus_id)
        if rarity is not None:
            q &= Q(rarity=rarity)
        if difficulty is not None:
            q &= Q(difficulty=difficulty)
        
        total, species_list = await plant_species_controller.list(page=page, page_size=page_size, search=q)
    else:
        # 原有的查询逻辑
        q = Q()
        if genus_id is not None:
            q &= Q(genus_id=genus_id)
        if rarity is not None:
            q &= Q(rarity=rarity)
        if difficulty is not None:
            q &= Q(difficulty=difficulty)
        
        total, species_list = await plant_species_controller.list(page=page, page_size=page_size, search=q)
    
    # 过滤名称（原有逻辑）
    if name:
        filtered_species = []
        for species in species_list:
            species_name = species.name
            if (name in species_name.get('chinese', '') or 
                name in species_name.get('english', '') or 
                name in species_name.get('latin', '')):
                filtered_species.append(species)
        species_list = filtered_species
        total = len(filtered_species)
    
    data = []
    for obj in species_list:
        species_dict = await obj.to_dict()
        # 添加属信息
        genus = await plant_genus_controller.get(id=obj.genus_id)
        species_dict["genus"] = await genus.to_dict()
        # 添加主图
        primary_image = await obj.images.filter(is_primary=True).first()
        if primary_image:
            species_dict["primary_image"] = await primary_image.to_dict()
        
        # 添加分类信息
        categories = await obj.categories.all()
        species_dict["categories"] = [await category.to_dict() for category in categories]
        
        data.append(species_dict)
    
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/species/get", summary="查看植物种详情")
async def get_plant_species(
    species_id: int = Query(..., description="种ID"),
):
    species = await plant_species_controller.get(id=species_id)
    species_dict = await species.to_dict()
    
    # 添加属信息
    genus = await plant_genus_controller.get(id=species.genus_id)
    species_dict["genus"] = await genus.to_dict()
    
    # 添加分类信息
    categories = await species.categories.all()
    species_dict["categories"] = [await category.to_dict() for category in categories]
    
    # 添加图片
    images = await species.images.all()
    species_dict["images"] = [await image.to_dict() for image in images]
    
    return Success(data=species_dict)


@router.post("/species/create", summary="创建植物种")
async def create_plant_species(
    species_in: PlantSpeciesCreate,
):
    # 检查属是否存在
    try:
        await plant_genus_controller.get(id=species_in.genus_id)
    except:
        return Fail(msg="指定的属不存在")
    
    # 创建植物种
    species = await plant_species_controller.create(obj_in=species_in)
    
    # 处理分类
    if species_in.category_ids:
        await plant_species_controller.add_categories(species.id, species_in.category_ids)
    
    species_dict = await species.to_dict()
    return Success(data=species_dict, msg="创建成功")


@router.post("/species/update", summary="更新植物种")
async def update_plant_species(
    species_in: PlantSpeciesUpdate,
):
    # 检查属是否存在
    if species_in.genus_id is not None:
        try:
            await plant_genus_controller.get(id=species_in.genus_id)
        except:
            return Fail(msg="指定的属不存在")
    
    # 更新植物种
    species = await plant_species_controller.update(id=species_in.id, obj_in=species_in)
    
    # 处理分类
    if species_in.category_ids is not None:
        await plant_species_controller.set_categories(species.id, species_in.category_ids)
    
    species_dict = await species.to_dict()
    return Success(data=species_dict, msg="更新成功")


# 添加植物分类管理 API
@router.post("/species/add_categories", summary="为植物添加分类")
async def add_species_categories(
    species_id: int = Body(..., description="植物ID", embed=True),
    category_ids: List[int] = Body(..., description="分类ID列表", embed=True),
):
    species = await plant_species_controller.add_categories(species_id, category_ids)
    species_dict = await species.to_dict()
    
    # 添加分类信息
    categories = await species.categories.all()
    species_dict["categories"] = [await category.to_dict() for category in categories]
    
    return Success(data=species_dict, msg="添加分类成功")


@router.post("/species/remove_categories", summary="移除植物的分类")
async def remove_species_categories(
    species_id: int = Body(..., description="植物ID", embed=True),
    category_ids: List[int] = Body(..., description="分类ID列表", embed=True),
):
    species = await plant_species_controller.remove_categories(species_id, category_ids)
    species_dict = await species.to_dict()
    
    # 添加分类信息
    categories = await species.categories.all()
    species_dict["categories"] = [await category.to_dict() for category in categories]
    
    return Success(data=species_dict, msg="移除分类成功")
    

@router.delete("/species/delete", summary="删除植物种")
async def delete_plant_species(
    species_id: int = Query(..., description="种ID"),
):
    # 删除关联的图片
    images = await plant_image_controller.get_by_plant(plant_id=species_id)
    for image in images:
        await plant_image_controller.remove(id=image.id)
    
    # 删除种
    await plant_species_controller.remove(id=species_id)
    return Success(msg="删除成功")


# 添加植物分类 API
@router.get("/category/list", summary="查看植物分类列表")
async def list_plant_category(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    name: str = Query("", description="分类名称，用于搜索"),
):
    q = Q()
    if name:
        q &= Q(name__contains=name)
    
    total, categories = await plant_category_controller.list(page=page, page_size=page_size, search=q)
    data = [await obj.to_dict() for obj in categories]
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/category/get", summary="查看植物分类详情")
async def get_plant_category(
    category_id: int = Query(..., description="分类ID"),
):
    category = await plant_category_controller.get(id=category_id)
    category_dict = await category.to_dict()
    return Success(data=category_dict)


@router.post("/category/create", summary="创建植物分类")
async def create_plant_category(
    category_in: PlantCategoryCreate,
):
    if await plant_category_controller.check_name_exists(category_in.name):
        return Fail(msg="分类名称已存在")

    category = await plant_category_controller.create(obj_in=category_in)
    category_dict = await category.to_dict()
    return Success(data=category_dict, msg="创建成功")


@router.post("/category/update", summary="更新植物分类")
async def update_plant_category(
    category_in: PlantCategoryUpdate,
):
    if await plant_category_controller.check_name_exists(category_in.name, exclude_id=category_in.id):
        return Fail(msg="分类名称已存在")
        
    category = await plant_category_controller.update(id=category_in.id, obj_in=category_in)
    category_dict = await category.to_dict()
    return Success(data=category_dict, msg="更新成功")


@router.delete("/category/delete", summary="删除植物分类")
async def delete_plant_category(
    category_id: int = Query(..., description="分类ID"),
):
    # 获取分类以检查是否有关联的植物
    category = await plant_category_controller.get(id=category_id)
    species_count = await category.species.all().count()
    if species_count > 0:
        return Fail(msg="无法删除：该分类下存在植物")
    
    await plant_category_controller.remove(id=category_id)
    return Success(msg="删除成功")


@router.get("/category/species", summary="查看特定分类下的植物列表")
async def list_species_by_category(
    category_id: int = Query(..., description="分类ID"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
):
    total, species_list = await plant_category_controller.get_species_by_category(
        category_id=category_id, page=page, page_size=page_size
    )
    
    data = []
    for obj in species_list:
        species_dict = await obj.to_dict()
        # 添加属信息
        genus = await plant_genus_controller.get(id=obj.genus_id)
        species_dict["genus"] = await genus.to_dict()
        # 添加主图
        primary_image = await obj.images.filter(is_primary=True).first()
        if primary_image:
            species_dict["primary_image"] = await primary_image.to_dict()
        data.append(species_dict)
    
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)



# 植物图片 API
@router.get("/image/list", summary="查看植物图片列表")
async def list_plant_image(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    plant_id: int = Query(..., description="植物ID"),
):
    q = Q(plant_id=plant_id)
    total, images = await plant_image_controller.list(
        page=page, page_size=page_size, search=q, order=["display_order"]
    )
    
    data = [await obj.to_dict() for obj in images]
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/image/get", summary="查看植物图片详情")
async def get_plant_image(
    image_id: int = Query(..., description="图片ID"),
):
    image = await plant_image_controller.get(id=image_id)
    image_dict = await image.to_dict()
    return Success(data=image_dict)


@router.post("/image/create", summary="添加植物图片")
async def create_plant_image(
    image_in: PlantImageCreate,
):
    # 检查植物是否存在
    try:
        await plant_species_controller.get(id=image_in.plant_id)
    except:
        return Fail(msg="指定的植物不存在")
    
    # 如果是主图，先将其他图片设为非主图
    if image_in.is_primary:
        await plant_image_controller.model.filter(plant_id=image_in.plant_id).update(is_primary=False)
    
    # 如果是第一张图片，自动设为主图
    count = await plant_image_controller.model.filter(plant_id=image_in.plant_id).count()
    if count == 0:
        image_in.is_primary = True
    
    await plant_image_controller.create(obj_in=image_in)
    return Success(msg="创建成功")


@router.post("/image/update", summary="更新植物图片")
async def update_plant_image(
    image_in: PlantImageUpdate,
):
    # 如果更新了植物ID，检查植物是否存在
    if image_in.plant_id is not None:
        try:
            await plant_species_controller.get(id=image_in.plant_id)
        except:
            return Fail(msg="指定的植物不存在")
    
    # 获取原始图片信息
    image = await plant_image_controller.get(id=image_in.id)
    
    # 如果设置为主图，先将其他图片设为非主图
    if image_in.is_primary is True:
        await plant_image_controller.model.filter(plant_id=image.plant_id).update(is_primary=False)
    
    await plant_image_controller.update(id=image_in.id, obj_in=image_in)
    return Success(msg="更新成功")


@router.delete("/image/delete", summary="删除植物图片")
async def delete_plant_image(
    image_id: int = Query(..., description="图片ID"),
):
    # 获取图片信息
    image = await plant_image_controller.get(id=image_id)
    is_primary = image.is_primary
    plant_id = image.plant_id
    
    # 删除图片
    await plant_image_controller.remove(id=image_id)
    
    # 如果删除的是主图，设置其他图片为主图
    if is_primary:
        other_image = await plant_image_controller.model.filter(plant_id=plant_id).first()
        if other_image:
            other_image.is_primary = True
            await other_image.save()
    
    return Success(msg="删除成功")


@router.post("/image/set_primary", summary="设置主图")
async def set_primary_image(
    image_id: int = Body(..., description="图片ID", embed=True),
):
    # 获取图片信息
    image = await plant_image_controller.get(id=image_id)
    
    # 设置为主图
    await plant_image_controller.set_primary(image_id=image_id, plant_id=image.plant_id)
    
    return Success(msg="设置成功")