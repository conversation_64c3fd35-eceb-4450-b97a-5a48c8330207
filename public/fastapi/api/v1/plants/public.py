from fastapi import APIRouter, Query
from app.controllers.plant import plant_species_controller, plant_family_controller, plant_genus_controller, plant_image_controller
from app.schemas.base import Success, SuccessExtra
from tortoise.expressions import Q
import random
from typing import Optional

public_plants_router = APIRouter()

# 1. 基于指定【属】查询下面全部【种】的列表
@public_plants_router.get("/species/by_genus", summary="查看指定属下的所有植物种")
async def list_species_by_genus(
    genus_id: int = Query(..., description="属ID"),
):
    # 验证属是否存在
    try:
        genus = await plant_genus_controller.get(id=genus_id)
    except:
        return {"code": 400, "msg": "指定的属不存在"}
    
    # 获取该属下的所有种
    species_list = await plant_species_controller.get_by_genus(genus_id=genus_id)
    
    # 构建返回数据
    data = []
    for species in species_list:
        species_dict = await species.to_dict()
        
        # 获取主图信息
        primary_image = await plant_image_controller.model.filter(
            plant_id=species.id, is_primary=True
        ).first()
        if primary_image:
            species_dict["primary_image"] = await primary_image.to_dict()
        
        # 添加属信息
        species_dict["genus"] = await genus.to_dict()
        
        # 添加科信息
        family = await plant_family_controller.get(id=genus.family_id)
        species_dict["family"] = await family.to_dict()
        
        data.append(species_dict)
    
    return Success(data=data)

# 2. 某个【种】的详情信息
@public_plants_router.get("/species/detail", summary="查看植物种详情")
async def get_species_detail(
    species_id: int = Query(..., description="种ID"),
):
    try:
        species_dict = await plant_species_controller.get_with_images(species_id=species_id)
        
        # 添加属信息
        species = await plant_species_controller.get(id=species_id)
        genus = await plant_genus_controller.get(id=species.genus_id)
        species_dict["genus"] = await genus.to_dict()
        
        # 添加科信息
        family = await plant_family_controller.get(id=genus.family_id)
        species_dict["family"] = await family.to_dict()
        
        return Success(data=species_dict)
    except:
        return {"code": 400, "msg": "指定的植物种不存在"}

# 3. 随机返回N个【种】
@public_plants_router.get("/species/random", summary="随机获取植物种")
async def get_random_species(
    count: int = Query(5, description="返回的数量", ge=1, le=20),
    family_id: Optional[int] = Query(None, description="科ID，用于筛选范围"),
    genus_id: Optional[int] = Query(None, description="属ID，用于筛选范围"),
):
    # 构建查询条件
    q = Q()
    if family_id is not None:
        # 如果指定了科，需要先获取该科下的所有属
        try:
            await plant_family_controller.get(id=family_id)
        except:
            return {"code": 400, "msg": "指定的科不存在"}
        
        genera = await plant_genus_controller.model.filter(family_id=family_id)
        genus_ids = [genus.id for genus in genera]
        if not genus_ids:
            return Success(data=[])  # 如果该科下没有属，返回空列表
        
        q &= Q(genus_id__in=genus_ids)
    
    if genus_id is not None:
        # 如果指定了属，直接筛选
        try:
            await plant_genus_controller.get(id=genus_id)
        except:
            return {"code": 400, "msg": "指定的属不存在"}
        
        q &= Q(genus_id=genus_id)
    
    # 获取所有符合条件的植物种
    all_species = await plant_species_controller.model.filter(q)
    
    # 随机选择指定数量的植物种
    if len(all_species) <= count:
        selected_species = all_species
    else:
        selected_species = random.sample(all_species, count)
    
    # 构建返回数据
    data = []
    for species in selected_species:
        species_dict = await species.to_dict()
        
        # 获取主图信息
        primary_image = await plant_image_controller.model.filter(
            plant_id=species.id, is_primary=True
        ).first()
        if primary_image:
            species_dict["primary_image"] = await primary_image.to_dict()
        
        # 添加属信息
        genus = await plant_genus_controller.get(id=species.genus_id)
        species_dict["genus"] = await genus.to_dict()
        
        # 添加科信息
        family = await plant_family_controller.get(id=genus.family_id)
        species_dict["family"] = await family.to_dict()
        
        data.append(species_dict)
    
    return Success(data=data)