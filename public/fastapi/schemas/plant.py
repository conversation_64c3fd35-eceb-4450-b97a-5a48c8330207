from enum import Enum
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field


# 枚举类型
class PropagationMethodEnum(str, Enum):
    SEED = "种子"
    CUTTING = "扦插"
    DIVISION = "分株"
    GRAFTING = "嫁接"
    SPORE = "孢子"
    OFFSET = "分生"
    LAYERING = "压条"
    LEAF_CUTTING = "叶插"


class SoilComponentEnum(str, Enum):
    PEAT = "泥炭土"
    PERLITE = "珍珠岩"
    COCO_COIR = "椰壳"
    SPHAGNUM = "水苔"
    AKADAMA = "赤玉土"
    VERMICULITE = "蛭石"
    LAVA_ROCK = "火山岩"
    PUMICE = "浮石"
    PINE_BARK = "松树皮"
    # = "鹿沼土"、


class RarityLevelEnum(int, Enum):
    VERY_COMMON = 1
    COMMON = 2
    UNCOMMON = 3
    RARE = 4
    VERY_RARE = 5


class DifficultyLevelEnum(int, Enum):
    VERY_EASY = 1
    EASY = 2
    MODERATE = 3
    DIFFICULT = 4
    VERY_DIFFICULT = 5


class WateringStyleEnum(str, Enum):
    SEE_DRY_SEE_WET = "见干见湿"
    DRY_THOROUGHLY_WATER_THOROUGHLY = "干透浇透"


# 基础模型
class TaxonomyModel(BaseModel):
    chinese: Optional[str] = Field(None, example="景天科", description="中文名")
    english: Optional[str] = Field(None, example="Crassulaceae", description="英文名")
    latin: Optional[str] = Field(None, example="Crassulaceae", description="拉丁名")


class RangeModel(BaseModel):
    min: Optional[float] = Field(None, description="最小值")
    max: Optional[float] = Field(None, description="最大值")


class SoilMixModel(BaseModel):
    components: Dict[str, int] = Field(default_factory=dict, description="土壤组分及其百分比")
    description: Optional[str] = Field(None, description="配土描述")
    


# 植物科
class BasePlantFamily(BaseModel):
    id: int
    name: TaxonomyModel
    description: Optional[str] = None


class PlantFamilyCreate(BaseModel):
    name: TaxonomyModel = Field(..., description="科名（中文、英文、拉丁文）")
    description: Optional[str] = Field(None, description="描述")


class PlantFamilyUpdate(BaseModel):
    id: int
    name: TaxonomyModel
    description: Optional[str] = None


# 植物属
class BasePlantGenus(BaseModel):
    id: int
    family_id: int
    name: TaxonomyModel
    description: Optional[str] = None


class PlantGenusCreate(BaseModel):
    family_id: int = Field(..., description="所属科ID")
    name: TaxonomyModel = Field(..., description="属名（中文、英文、拉丁文）")
    description: Optional[str] = Field(None, description="描述")


class PlantGenusUpdate(BaseModel):
    id: int
    family_id: int
    name: TaxonomyModel
    description: Optional[str] = None


# 植物种
class BasePlantSpecies(BaseModel):
    id: int
    genus_id: int
    name: TaxonomyModel
    alias: Optional[str] = None
    description: Optional[str] = None
    rarity: Optional[RarityLevelEnum] = None
    difficulty: Optional[DifficultyLevelEnum] = None
    image: Optional[str] = None
    
    # 环境需求
    temperature_range: Optional[RangeModel] = None
    temperature_extreme: Optional[RangeModel] = None
    light_range: Optional[RangeModel] = None
    light_extreme: Optional[RangeModel] = None
    humidity_range: Optional[RangeModel] = None
    humidity_extreme: Optional[RangeModel] = None
    
    # 原产地和栖息地
    origin: Optional[List[str]] = None
    habitat: Optional[str] = None
    
    # 浇水习性
    watering_notes: Optional[str] = None
    watering_style: Optional[WateringStyleEnum] = None
    
    # 繁殖方式
    sexual_propagation: Optional[bool] = False
    asexual_propagation: Optional[List[str]] = None
    
    # 配土
    soil_mix: Optional[SoilMixModel] = None
    
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class PlantCategoryBase(BaseModel):
    name: str
    description: Optional[str] = None

class PlantCategoryCreate(PlantCategoryBase):
    pass

class PlantCategoryUpdate(PlantCategoryBase):
    id: int

class PlantSpeciesCreate(BaseModel):
    genus_id: int = Field(..., description="所属属ID")
    name: TaxonomyModel = Field(..., description="种名（中文、英文、拉丁文）")
    alias: Optional[str] = Field(None, description="别名，以逗号分隔")
    description: Optional[str] = Field(None, description="植物详细描述，支持Markdown格式")
    category_ids: Optional[List[int]] = None
    rarity: Optional[RarityLevelEnum] = Field(None, description="市场稀缺性")
    difficulty: Optional[DifficultyLevelEnum] = Field(None, description="养护难度")
    image: Optional[str] = Field(None, description="主图URL")
    
    # 环境需求
    temperature_range: Optional[RangeModel] = Field(None, description="适宜温度范围，单位：摄氏度")
    temperature_extreme: Optional[RangeModel] = Field(None, description="可接受极限温度范围，单位：摄氏度")
    light_range: Optional[RangeModel] = Field(None, description="适宜光照范围，单位：Lux")
    light_extreme: Optional[RangeModel] = Field(None, description="可接受极限光照范围，单位：Lux")
    humidity_range: Optional[RangeModel] = Field(None, description="适宜湿度范围，单位：百分比")
    humidity_extreme: Optional[RangeModel] = Field(None, description="可接受极限湿度范围，单位：百分比")
    
    # 原产地和栖息地
    origin: Optional[List[str]] = Field(None, description="原产地列表")
    habitat: Optional[str] = Field(None, description="原生环境描述，支持Markdown格式")
    
    # 浇水习性
    watering_notes: Optional[str] = Field(None, description="浇水注意事项")
    watering_style: Optional[WateringStyleEnum] = Field(None, description="浇水方式")
    
    # 繁殖方式
    sexual_propagation: Optional[bool] = Field(False, description="是否可以通过种子繁殖")
    asexual_propagation: Optional[List[str]] = Field(None, description="无性繁殖方式列表")
    
    # 配土
    soil_mix: Optional[SoilMixModel] = Field(None, description="配土组合及其百分比和描述")


class PlantSpeciesUpdate(BaseModel):
    id: int
    genus_id: Optional[int] = None
    name: Optional[TaxonomyModel] = None
    alias: Optional[str] = None
    description: Optional[str] = None
    category_ids: Optional[List[int]] = None
    rarity: Optional[RarityLevelEnum] = None
    difficulty: Optional[DifficultyLevelEnum] = None
    image: Optional[str] = None
    
    # 环境需求
    temperature_range: Optional[RangeModel] = None
    temperature_extreme: Optional[RangeModel] = None
    light_range: Optional[RangeModel] = None
    light_extreme: Optional[RangeModel] = None
    humidity_range: Optional[RangeModel] = None
    humidity_extreme: Optional[RangeModel] = None
    
    # 原产地和栖息地
    origin: Optional[List[str]] = None
    habitat: Optional[str] = None
    
    # 浇水习性
    watering_notes: Optional[str] = None
    watering_style: Optional[WateringStyleEnum] = None
    
    # 繁殖方式
    sexual_propagation: Optional[bool] = None
    asexual_propagation: Optional[List[str]] = None
    
    # 配土
    soil_mix: Optional[SoilMixModel] = None


# 植物图片
class BasePlantImage(BaseModel):
    id: int
    plant_id: int
    image_url: str
    caption: Optional[str] = None
    is_primary: bool = False
    display_order: int = 0
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class PlantImageCreate(BaseModel):
    plant_id: int = Field(..., description="所属植物ID")
    image_url: str = Field(..., description="图片URL")
    caption: Optional[str] = Field(None, description="图片说明")
    is_primary: bool = Field(False, description="是否为主图")
    display_order: int = Field(0, description="显示顺序")


class PlantImageUpdate(BaseModel):
    id: int
    plant_id: Optional[int] = None
    image_url: Optional[str] = None
    caption: Optional[str] = None
    is_primary: Optional[bool] = None
    display_order: Optional[int] = None


# 完整的植物信息（包含图片）
class PlantSpeciesWithImages(BasePlantSpecies):
    images: Optional[List[BasePlantImage]] = []