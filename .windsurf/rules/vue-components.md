---
trigger: manual
---

请严格按照以下规范开发Vue3组件，确保代码质量和一致性：

1. 基础架构要求

- 必须使用组合式API (Composition API) - 使用 <script setup lang="ts">
- TypeScript支持 - 所有组件必须使用TypeScript
- CSS框架 - 优先使用 Tailwind CSS 4.0进行样式设计，直接写在标签内，仅在 Tailwind 无法满足需求时才在 <style scoped>中定义
- 响应式设计 - 组件应尽量充满容器，以适配不同使用场景
- 如果需要使用图标，请使用 iconify 对应的图标
- 一定程度的注释

2. 代码结构模板

<script setup lang="ts">
import { computed, ref, withDefaults } from 'vue'
// 导入必要的外部依赖

// ===================== 类型定义区域 =====================
// 定义业务相关的基础接口
interface BaseDataType {
  // 定义核心数据结构
}

// 定义 Props 接口 - 描述组件对外暴露的所有属性
interface Props {
  // 必需属性 - 使用联合类型限制可选值
  requiredProp: 'option1' | 'option2' | 'option3'
  
  // 复杂类型属性
  complexData: BaseDataType
  
  // 可选属性 - 提供合理的默认值
  optionalProp?: string
  theme?: string
  disabled?: boolean
}

// ===================== Props 和默认值 =====================
// 使用 withDefaults 设置智能默认值
const props = withDefaults(defineProps<Props>(), {
  // 静态默认值
  optionalProp: 'defaultValue',
  disabled: false,
  
  // 动态默认值 - 基于其他 props 计算
  theme: (props) => {
    // 根据其他属性计算合适的默认值
    return computeDefaultTheme(props.requiredProp)
  }
})

// ===================== 响应式状态区域 =====================
// 组件内部状态 - 使用描述性命名
const isInteractionActive = ref(false)
const currentActiveIndex = ref(-1)
const internalStateData = ref<DataType[]>([])

// ===================== 计算属性区域 =====================
// 基于 props 计算的衍生状态
const computedDisplayData = computed(() => {
  // 基于 props 和内部状态计算最终展示数据
  return transformDataForDisplay(props.complexData, internalStateData.value)
})

// 样式相关计算属性
const dynamicStyleConfig = computed(() => {
  // 计算动态样式配置
  return {
    // 返回样式对象
  }
})

// 交互状态计算属性
const interactionStateConfig = computed(() => {
  // 计算交互相关的状态配置
  return {
    // 返回交互配置对象
  }
})

// ===================== 事件处理函数区域 =====================
// 用户交互处理 - 使用 handle + 动作 + 目标 的命名规范
const handleUserInteraction = (data: DataType, context?: any) => {
  // 处理用户交互逻辑
  updateInternalState(data)
}

const handleStateTransition = () => {
  // 处理状态变更逻辑
  // 先处理依赖状态
  // 再更新目标状态
}

// ===================== 工具函数区域 =====================
// 纯函数 - 无副作用的数据处理函数
const transformDataForDisplay = (sourceData: BaseDataType, additionalData: DataType[]) => {
  // 数据转换逻辑
  return processedData
}

const computeDefaultTheme = (baseType: string) => {
  // 默认值计算逻辑
  return defaultValue
}

const updateInternalState = (newData: DataType) => {
  // 状态更新逻辑封装
}
</script>
